use std::{
    fs,
    path::PathBuf,
    sync::{Arc, Mutex},
    time::Duration,
};

use coco_server::{
    config::config_manager::ConfigManager,
    services::{config_reload_service::ConfigReloadService, ReloadStatus},
};
use tempfile::TempDir;
use tokio::time::sleep;

/// 创建测试配置文件
fn create_test_config(dir: &TempDir, content: &str) -> PathBuf {
    let config_path = dir.path().join("test_config.yml");
    fs::write(&config_path, content).expect("Failed to write test config");
    config_path
}

/// 创建测试配置管理器
fn create_test_config_manager(config_path: &PathBuf) -> Arc<Mutex<ConfigManager>> {
    // 设置环境变量指向测试配置文件
    std::env::set_var("COCO_CONFIG_PATH", config_path.to_str().unwrap());

    let config_manager = ConfigManager::new().expect("Failed to create config manager");
    Arc::new(Mutex::new(config_manager))
}

#[tokio::test]
async fn test_config_reload_service_creation() {
    let temp_dir = TempDir::new().expect("Failed to create temp dir");
    let config_content = r#"
env:
  api_binding: "0.0.0.0:2900"
  web_binding: "0.0.0.0:9000"
database:
  url: "127.0.0.1:8000"
  namespace: "test"
  database: "test"
  username: "root"
  password: "root"
"#;

    let config_path = create_test_config(&temp_dir, config_content);
    let config_manager = create_test_config_manager(&config_path);

    let reload_service = ConfigReloadService::new(config_manager, &config_path);
    assert!(reload_service.is_ok());

    let service = reload_service.unwrap();
    assert_eq!(service.get_config_path(), config_path);
    assert!(!service.is_auto_reload_enabled());

    // 检查初始状态
    match service.get_status() {
        ReloadStatus::Idle => {}
        _ => panic!("Expected Idle status"),
    }

    // 检查初始统计信息
    let stats = service.get_stats();
    assert_eq!(stats.total_reloads, 0);
    assert_eq!(stats.successful_reloads, 0);
    assert_eq!(stats.failed_reloads, 0);
}

#[tokio::test]
async fn test_manual_config_reload_success() {
    let temp_dir = TempDir::new().expect("Failed to create temp dir");
    let config_content = r#"
env:
  api_binding: "0.0.0.0:2900"
  web_binding: "0.0.0.0:9000"
database:
  url: "127.0.0.1:8000"
  namespace: "test"
  database: "test"
  username: "root"
  password: "root"
"#;

    let config_path = create_test_config(&temp_dir, config_content);
    let config_manager = create_test_config_manager(&config_path);

    let service = ConfigReloadService::new(config_manager, &config_path)
        .expect("Failed to create reload service");

    // 执行手动重载
    let result = service.reload_config().await;
    assert!(result.is_ok(), "Manual reload should succeed");

    // 检查状态
    match service.get_status() {
        ReloadStatus::Success => {}
        status => panic!("Expected Success status, got: {:?}", status),
    }

    // 检查统计信息
    let stats = service.get_stats();
    assert_eq!(stats.total_reloads, 1);
    assert_eq!(stats.successful_reloads, 1);
    assert_eq!(stats.failed_reloads, 0);
    assert!(stats.last_reload_time.is_some());
    assert!(stats.last_successful_reload.is_some());
}

#[tokio::test]
async fn test_config_reload_with_invalid_config() {
    let temp_dir = TempDir::new().expect("Failed to create temp dir");
    let valid_config = r#"
env:
  api_binding: "0.0.0.0:2900"
  web_binding: "0.0.0.0:9000"
database:
  url: "127.0.0.1:8000"
  namespace: "test"
  database: "test"
  username: "root"
  password: "root"
"#;

    let config_path = create_test_config(&temp_dir, valid_config);
    let config_manager = create_test_config_manager(&config_path);

    let service = ConfigReloadService::new(config_manager, &config_path)
        .expect("Failed to create reload service");

    // 先执行一次成功的重载来创建备份
    let result = service.reload_config().await;
    assert!(result.is_ok(), "Initial reload should succeed");

    // 写入无效配置
    let invalid_config = r#"
invalid_yaml: [
  missing_closing_bracket
"#;
    fs::write(&config_path, invalid_config).expect("Failed to write invalid config");

    // 尝试重载，应该失败并回滚
    let result = service.reload_config().await;
    assert!(result.is_err(), "Reload with invalid config should fail");

    // 检查状态
    match service.get_status() {
        ReloadStatus::RolledBack(_) => {}
        status => panic!("Expected RolledBack status, got: {:?}", status),
    }

    // 检查统计信息
    let stats = service.get_stats();
    assert_eq!(stats.total_reloads, 2); // 一次成功，一次失败
    assert_eq!(stats.successful_reloads, 1); // 第一次成功
    assert_eq!(stats.failed_reloads, 1); // 第二次失败
    assert_eq!(stats.rollback_count, 1);
}

#[tokio::test]
async fn test_hot_reload_functionality() {
    let temp_dir = TempDir::new().expect("Failed to create temp dir");
    let config_content = r#"
env:
  api_binding: "0.0.0.0:2900"
  web_binding: "0.0.0.0:9000"
database:
  url: "127.0.0.1:8000"
  namespace: "test"
  database: "test"
  username: "root"
  password: "root"
"#;

    let config_path = create_test_config(&temp_dir, config_content);
    let config_manager = create_test_config_manager(&config_path);

    let service = ConfigReloadService::new(config_manager, &config_path)
        .expect("Failed to create reload service");

    // 启动热重载
    let result = service.start_hot_reload().await;
    assert!(result.is_ok(), "Starting hot reload should succeed");
    assert!(service.is_auto_reload_enabled());

    // 修改配置文件
    let updated_config = r#"
env:
  api_binding: "0.0.0.0:2901"  # 修改端口
  web_binding: "0.0.0.0:9001"
database:
  url: "127.0.0.1:8000"
  namespace: "test"
  database: "test"
  username: "root"
  password: "root"
"#;
    fs::write(&config_path, updated_config).expect("Failed to write updated config");

    // 等待文件监控器检测到变更并触发重载
    sleep(Duration::from_millis(1000)).await;

    // 停止热重载
    service.stop_hot_reload();
    assert!(!service.is_auto_reload_enabled());
}

#[tokio::test]
async fn test_backup_and_rollback() {
    let temp_dir = TempDir::new().expect("Failed to create temp dir");
    let original_config = r#"
env:
  api_binding: "0.0.0.0:2900"
  web_binding: "0.0.0.0:9000"
database:
  url: "127.0.0.1:8000"
  namespace: "test"
  database: "test"
  username: "root"
  password: "root"
"#;

    let config_path = create_test_config(&temp_dir, original_config);
    let config_manager = create_test_config_manager(&config_path);

    let service = ConfigReloadService::new(config_manager, &config_path)
        .expect("Failed to create reload service");

    // 首次成功重载，创建备份
    let result = service.reload_config().await;
    assert!(result.is_ok());

    // 写入无效配置
    let invalid_config = "invalid: yaml: content: [";
    fs::write(&config_path, invalid_config).expect("Failed to write invalid config");

    // 尝试重载，应该失败并回滚
    let result = service.reload_config().await;
    assert!(result.is_err());

    // 验证配置已回滚到原始内容
    let current_content = fs::read_to_string(&config_path).expect("Failed to read config");
    assert!(current_content.contains("api_binding: \"0.0.0.0:2900\""));

    // 清理备份文件
    let cleanup_result = service.cleanup_backup();
    assert!(cleanup_result.is_ok());
}

#[tokio::test]
async fn test_reload_statistics() {
    let temp_dir = TempDir::new().expect("Failed to create temp dir");
    let config_content = r#"
env:
  api_binding: "0.0.0.0:2900"
  web_binding: "0.0.0.0:9000"
database:
  url: "127.0.0.1:8000"
  namespace: "test"
  database: "test"
  username: "root"
  password: "root"
"#;

    let config_path = create_test_config(&temp_dir, config_content);
    let config_manager = create_test_config_manager(&config_path);

    let service = ConfigReloadService::new(config_manager, &config_path)
        .expect("Failed to create reload service");

    // 执行多次重载
    for i in 0..3 {
        let result = service.reload_config().await;
        assert!(result.is_ok(), "Reload {} should succeed", i + 1);
    }

    // 检查统计信息
    let stats = service.get_stats();
    assert_eq!(stats.total_reloads, 3);
    assert_eq!(stats.successful_reloads, 3);
    assert_eq!(stats.failed_reloads, 0);
    assert!(stats.average_reload_time_ms > 0.0);
    assert!(stats.last_reload_time.is_some());
    assert!(stats.last_successful_reload.is_some());
}
