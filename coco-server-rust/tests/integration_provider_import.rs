use chrono::Utc;
use coco_server::{
    models::model_provider::ModelProvider,
    services::import_strategy::{ImportAction, ImportStrategy},
};

/// 集成测试：验证提供商导入系统的完整功能
#[tokio::test]
async fn test_provider_import_system_integration() {
    // 测试1: 验证导入策略处理器的基本功能
    test_import_strategy_processor().await;

    // 测试2: 验证导入结果统计
    test_import_result_statistics().await;

    println!("✅ 所有提供商导入系统集成测试通过");
}

async fn test_import_strategy_processor() {
    use coco_server::services::import_strategy::ImportStrategyProcessor;

    println!("🧪 测试导入策略处理器...");

    // 创建测试提供商
    let new_provider = create_test_provider("test-1", "New Provider", true);
    let mut existing_provider = create_test_provider("test-1", "Existing Provider", false);
    existing_provider.api_key = "secret-key".to_string();

    // 测试跳过策略
    let skip_processor = ImportStrategyProcessor::new(ImportStrategy::SkipExisting);
    let result = skip_processor
        .process_provider(&new_provider, Some(&existing_provider))
        .unwrap();
    assert!(result.is_none(), "跳过策略应该返回None");

    // 测试更新策略
    let update_processor = ImportStrategyProcessor::new(ImportStrategy::UpdateExisting);
    let result = update_processor
        .process_provider(&new_provider, Some(&existing_provider))
        .unwrap();
    let updated = result.unwrap();
    assert_eq!(updated.name, "New Provider", "名称应该更新");
    assert_eq!(updated.api_key, "secret-key", "API密钥应该保留");
    assert_eq!(updated.enabled, false, "enabled状态应该保留");
    assert_eq!(updated.id, existing_provider.id, "ID应该保留");

    // 测试强制覆盖策略
    let overwrite_processor = ImportStrategyProcessor::new(ImportStrategy::ForceOverwrite);
    let result = overwrite_processor
        .process_provider(&new_provider, Some(&existing_provider))
        .unwrap();
    let overwritten = result.unwrap();
    assert_eq!(overwritten.name, "New Provider", "名称应该更新");
    assert_eq!(overwritten.api_key, "", "API密钥应该被覆盖");
    assert_eq!(overwritten.enabled, true, "enabled状态应该被覆盖");

    // 测试新建提供商
    let result = update_processor
        .process_provider(&new_provider, None)
        .unwrap();
    let created = result.unwrap();
    assert_eq!(created.name, "New Provider", "新建提供商名称正确");
    assert_eq!(created.builtin, true, "新建提供商应该标记为内置");

    println!("✅ 导入策略处理器测试通过");
}

async fn test_import_result_statistics() {
    use coco_server::services::import_strategy::{ImportDetail, ImportResult};

    println!("🧪 测试导入结果统计...");

    let mut result = ImportResult::new(ImportStrategy::UpdateExisting);

    // 添加各种类型的导入详情
    result.add_detail(ImportDetail {
        provider_id: "1".to_string(),
        provider_name: "Provider 1".to_string(),
        action: ImportAction::Created,
        success: true,
        error: None,
        duration_ms: 100,
        timestamp: Utc::now(),
    });

    result.add_detail(ImportDetail {
        provider_id: "2".to_string(),
        provider_name: "Provider 2".to_string(),
        action: ImportAction::Updated,
        success: true,
        error: None,
        duration_ms: 150,
        timestamp: Utc::now(),
    });

    result.add_detail(ImportDetail {
        provider_id: "3".to_string(),
        provider_name: "Provider 3".to_string(),
        action: ImportAction::Skipped,
        success: true,
        error: None,
        duration_ms: 50,
        timestamp: Utc::now(),
    });

    result.add_detail(ImportDetail {
        provider_id: "4".to_string(),
        provider_name: "Provider 4".to_string(),
        action: ImportAction::Failed,
        success: false,
        error: Some("测试错误".to_string()),
        duration_ms: 200,
        timestamp: Utc::now(),
    });

    result.finish();

    // 验证统计结果
    assert_eq!(result.imported_count, 1, "新建数量应该为1");
    assert_eq!(result.updated_count, 1, "更新数量应该为1");
    assert_eq!(result.skipped_count, 1, "跳过数量应该为1");
    assert_eq!(result.failed_count, 1, "失败数量应该为1");
    assert_eq!(result.total_count, 4, "总数量应该为4");
    assert!(result.has_failures(), "应该有失败的导入");
    assert_eq!(result.success_rate(), 0.75, "成功率应该为75%");

    println!("✅ 导入结果统计测试通过");
}

fn create_test_provider(id: &str, name: &str, enabled: bool) -> ModelProvider {
    ModelProvider {
        id: id.to_string(),
        created: Utc::now(),
        updated: Utc::now(),
        name: name.to_string(),
        api_key: "".to_string(),
        api_type: "openai".to_string(),
        base_url: "https://api.example.com".to_string(),
        icon: "test_icon".to_string(),
        models: vec![],
        enabled,
        builtin: true,
        description: "测试提供商".to_string(),
    }
}

/// 测试导入策略的显示格式
#[test]
fn test_import_strategy_display() {
    use coco_server::services::import_strategy::{ImportAction, ImportStrategy};

    assert_eq!(ImportStrategy::SkipExisting.to_string(), "跳过已存在");
    assert_eq!(ImportStrategy::UpdateExisting.to_string(), "更新已存在");
    assert_eq!(ImportStrategy::ForceOverwrite.to_string(), "强制覆盖");

    assert_eq!(ImportAction::Created.to_string(), "新建");
    assert_eq!(ImportAction::Updated.to_string(), "更新");
    assert_eq!(ImportAction::Skipped.to_string(), "跳过");
    assert_eq!(ImportAction::Failed.to_string(), "失败");
}

/// 测试导入策略的默认值
#[test]
fn test_import_strategy_default() {
    use coco_server::services::import_strategy::ImportStrategy;

    let default_strategy = ImportStrategy::default();
    assert_eq!(default_strategy, ImportStrategy::UpdateExisting);
}
