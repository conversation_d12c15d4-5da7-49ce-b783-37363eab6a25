use criterion::{black_box, criterion_group, criterion_main, BenchmarkId, Criterion};
use std::sync::Arc;
use std::time::Duration;
use tokio::runtime::Runtime;

use coco_server::{
    models::model_provider::ModelProvider,
    repositories::{
        model_provider_repo::{SurrealModelProviderRepository, ModelProviderRepository, SearchQuery},
        cache_manager::CacheManager,
    },
    database::client::DatabaseClient,
};

/// 创建测试用的模型提供商
fn create_test_model_provider(name: &str) -> ModelProvider {
    ModelProvider {
        id: format!("test_{}", name),
        name: name.to_string(),
        api_type: "openai".to_string(),
        api_key: "test-api-key".to_string(),
        base_url: Some("https://api.openai.com/v1".to_string()),
        description: Some(format!("测试提供商 {}", name)),
        enabled: true,
        builtin: false,
        models: vec![],
        created: chrono::Utc::now(),
        updated: chrono::Utc::now(),
    }
}

/// 设置测试数据库环境
async fn setup_test_database() -> Arc<SurrealModelProviderRepository> {
    let db_client = Arc::new(
        DatabaseClient::new("memory", "bench_test", "test", "test")
            .await
            .expect("Failed to create database client")
    );

    Arc::new(SurrealModelProviderRepository::new(db_client))
}

/// 基准测试：数据库连接性能
fn benchmark_database_connection(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();

    let mut group = c.benchmark_group("database_connection");
    group.measurement_time(Duration::from_secs(10));

    // 测试单个连接创建
    group.bench_function("single_connection", |b| {
        b.iter(|| {
            rt.block_on(async {
                black_box(
                    DatabaseClient::new("memory", "conn_test", "test", "test").await
                )
            })
        });
    });

    // 测试并发连接创建
    for concurrency in [5, 10, 20].iter() {
        group.bench_with_input(
            BenchmarkId::new("concurrent_connections", concurrency),
            concurrency,
            |b, &concurrency| {
                b.iter(|| {
                    rt.block_on(async {
                        let mut handles = Vec::new();
                        for i in 0..concurrency {
                            let handle = tokio::spawn(async move {
                                DatabaseClient::new("memory", &format!("conn_test_{}", i), "test", "test").await
                            });
                            handles.push(handle);
                        }

                        // 等待所有连接完成
                        for handle in handles {
                            let _ = black_box(handle.await);
                        }
                    })
                });
            },
        );
    }

    group.finish();
}

/// 基准测试：数据库插入性能
fn benchmark_database_insert(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();
    let repository = rt.block_on(setup_test_database());

    let mut group = c.benchmark_group("database_insert");
    group.measurement_time(Duration::from_secs(10));

    // 测试单个插入操作
    group.bench_function("single_insert", |b| {
        let mut counter = 0;
        b.iter(|| {
            rt.block_on(async {
                counter += 1;
                let provider = create_test_model_provider(&format!("insert_test_{}", counter));
                black_box(repository.create(&provider).await)
            })
        });
    });

    // 测试批量插入操作
    for batch_size in [10, 50, 100].iter() {
        group.bench_with_input(
            BenchmarkId::new("batch_insert", batch_size),
            batch_size,
            |b, &batch_size| {
                b.iter(|| {
                    rt.block_on(async {
                        let mut handles = Vec::new();
                        for i in 0..batch_size {
                            let repository = repository.clone();
                            let provider = create_test_model_provider(&format!("batch_insert_{}_{}", batch_size, i));
                            let handle = tokio::spawn(async move {
                                repository.create(&provider).await
                            });
                            handles.push(handle);
                        }

                        // 等待所有插入完成
                        for handle in handles {
                            let _ = black_box(handle.await);
                        }
                    })
                });
            },
        );
    }

    group.finish();
}

/// 基准测试：数据库查询性能
fn benchmark_database_query(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();
    let repository = rt.block_on(setup_test_database());

    // 预先插入测试数据
    let provider_ids: Vec<String> = rt.block_on(async {
        let mut ids = Vec::new();
        for i in 0..1000 {
            let provider = create_test_model_provider(&format!("query_test_{}", i));
            if let Ok(id) = repository.create(&provider).await {
                ids.push(id);
            }
        }
        ids
    });

    let mut group = c.benchmark_group("database_query");
    group.measurement_time(Duration::from_secs(10));

    // 测试按ID查询
    group.bench_function("query_by_id", |b| {
        let mut index = 0;
        b.iter(|| {
            rt.block_on(async {
                let id = &provider_ids[index % provider_ids.len()];
                index += 1;
                black_box(repository.find_by_id(id).await)
            })
        });
    });

    // 测试按名称查询
    group.bench_function("query_by_name", |b| {
        let mut index = 0;
        b.iter(|| {
            rt.block_on(async {
                let name = format!("query_test_{}", index % 1000);
                index += 1;
                black_box(repository.find_by_name(&name).await)
            })
        });
    });

    // 测试并发查询
    for concurrency in [10, 50, 100].iter() {
        group.bench_with_input(
            BenchmarkId::new("concurrent_query", concurrency),
            concurrency,
            |b, &concurrency| {
                b.iter(|| {
                    rt.block_on(async {
                        let mut handles = Vec::new();
                        for i in 0..concurrency {
                            let repository = repository.clone();
                            let id = provider_ids[i % provider_ids.len()].clone();
                            let handle = tokio::spawn(async move {
                                repository.find_by_id(&id).await
                            });
                            handles.push(handle);
                        }

                        // 等待所有查询完成
                        for handle in handles {
                            let _ = black_box(handle.await);
                        }
                    })
                });
            },
        );
    }

    group.finish();
}

/// 基准测试：数据库更新性能
fn benchmark_database_update(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();
    let repository = rt.block_on(setup_test_database());

    // 预先插入测试数据
    let provider_ids: Vec<String> = rt.block_on(async {
        let mut ids = Vec::new();
        for i in 0..100 {
            let provider = create_test_model_provider(&format!("update_test_{}", i));
            if let Ok(id) = repository.create(&provider).await {
                ids.push(id);
            }
        }
        ids
    });

    let mut group = c.benchmark_group("database_update");
    group.measurement_time(Duration::from_secs(10));

    // 测试单个更新操作
    group.bench_function("single_update", |b| {
        let mut index = 0;
        b.iter(|| {
            rt.block_on(async {
                let id = &provider_ids[index % provider_ids.len()];
                index += 1;
                let mut provider = create_test_model_provider(&format!("updated_test_{}", index));
                provider.id = id.clone();
                black_box(repository.update(&provider).await)
            })
        });
    });

    // 测试并发更新操作
    for concurrency in [5, 10, 20].iter() {
        group.bench_with_input(
            BenchmarkId::new("concurrent_update", concurrency),
            concurrency,
            |b, &concurrency| {
                b.iter(|| {
                    rt.block_on(async {
                        let mut handles = Vec::new();
                        for i in 0..concurrency {
                            let repository = repository.clone();
                            let id = provider_ids[i % provider_ids.len()].clone();
                            let mut provider = create_test_model_provider(&format!("concurrent_update_{}", i));
                            provider.id = id;
                            let handle = tokio::spawn(async move {
                                repository.update(&provider).await
                            });
                            handles.push(handle);
                        }

                        // 等待所有更新完成
                        for handle in handles {
                            let _ = black_box(handle.await);
                        }
                    })
                });
            },
        );
    }

    group.finish();
}

/// 基准测试：数据库删除性能
fn benchmark_database_delete(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();
    let repository = rt.block_on(setup_test_database());

    let mut group = c.benchmark_group("database_delete");
    group.measurement_time(Duration::from_secs(10));

    // 测试单个删除操作
    group.bench_function("single_delete", |b| {
        let mut counter = 0;
        b.iter(|| {
            rt.block_on(async {
                // 每次测试前创建一个新的记录
                counter += 1;
                let provider = create_test_model_provider(&format!("delete_test_{}", counter));
                if let Ok(id) = repository.create(&provider).await {
                    black_box(repository.delete(&id).await)
                } else {
                    Ok(())
                }
            })
        });
    });

    group.finish();
}

/// 基准测试：数据库搜索性能
fn benchmark_database_search(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();
    let repository = rt.block_on(setup_test_database());

    // 预先插入大量测试数据
    rt.block_on(async {
        for i in 0..500 {
            let provider = create_test_model_provider(&format!("search_perf_test_{}", i));
            let _ = repository.create(&provider).await;
        }
    });

    let mut group = c.benchmark_group("database_search");
    group.measurement_time(Duration::from_secs(10));

    // 测试基础搜索
    group.bench_function("basic_search", |b| {
        b.iter(|| {
            rt.block_on(async {
                let query = SearchQuery {
                    query: Some("search_perf_test".to_string()),
                    enabled: None,
                    builtin: None,
                    api_type: None,
                    from: 0,
                    size: 20,
                    sort: None,
                };
                black_box(repository.search(&query).await)
            })
        });
    });

    // 测试不同页面大小的搜索性能
    for page_size in [10, 50, 100, 200].iter() {
        group.bench_with_input(
            BenchmarkId::new("search_page_size", page_size),
            page_size,
            |b, &page_size| {
                b.iter(|| {
                    rt.block_on(async {
                        let query = SearchQuery {
                            query: None,
                            enabled: None,
                            builtin: None,
                            api_type: None,
                            from: 0,
                            size: page_size,
                            sort: None,
                        };
                        black_box(repository.search(&query).await)
                    })
                });
            },
        );
    }

    // 测试过滤搜索
    group.bench_function("filtered_search", |b| {
        b.iter(|| {
            rt.block_on(async {
                let query = SearchQuery {
                    query: None,
                    enabled: Some(true),
                    builtin: Some(false),
                    api_type: Some("openai".to_string()),
                    from: 0,
                    size: 50,
                    sort: None,
                };
                black_box(repository.search(&query).await)
            })
        });
    });

    group.finish();
}

criterion_group!(
    benches,
    benchmark_database_connection,
    benchmark_database_insert,
    benchmark_database_query,
    benchmark_database_update,
    benchmark_database_delete,
    benchmark_database_search
);
criterion_main!(benches);
