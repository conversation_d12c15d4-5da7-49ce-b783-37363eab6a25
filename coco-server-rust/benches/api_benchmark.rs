use criterion::{black_box, criterion_group, criterion_main, BenchmarkId, Criterion};
use std::sync::Arc;
use std::time::Duration;
use tokio::runtime::Runtime;

use coco_server::{
    models::model_provider::{CreateModelProviderRequest, UpdateModelProviderRequest, ModelProvider},
    repositories::{
        model_provider_repo::{SurrealModelProviderRepository, SearchQuery},
        cache_manager::CacheManager,
    },
    services::{
        model_provider_service::ModelProviderService,
        cache_service::CacheService,
        validation_service::ValidationService,
    },
    database::client::DatabaseClient,
};

/// 创建测试用的模型提供商请求
fn create_test_provider_request(name: &str) -> CreateModelProviderRequest {
    CreateModelProviderRequest {
        name: name.to_string(),
        api_type: "openai".to_string(),
        api_key: "test-api-key".to_string(),
        base_url: Some("https://api.openai.com/v1".to_string()),
        description: Some(format!("测试提供商 {}", name)),
        enabled: true,
        builtin: false,
        models: vec![],
    }
}

/// 创建测试用的更新请求
fn create_test_update_request() -> UpdateModelProviderRequest {
    UpdateModelProviderRequest {
        name: Some("updated-provider".to_string()),
        api_type: Some("openai".to_string()),
        api_key: Some("updated-api-key".to_string()),
        base_url: Some("https://api.openai.com/v1".to_string()),
        description: Some("更新的测试提供商".to_string()),
        enabled: Some(false),
        models: Some(vec![]),
    }
}

/// 设置测试环境
async fn setup_test_environment() -> Arc<ModelProviderService> {
    // 创建数据库客户端
    let db_client = Arc::new(
        DatabaseClient::new("memory", "test", "test", "test")
            .await
            .expect("Failed to create database client")
    );

    // 创建仓储
    let repository = Arc::new(SurrealModelProviderRepository::new(db_client));

    // 创建缓存服务
    let cache_manager = Arc::new(CacheManager::new(Duration::from_secs(1800), 1000));
    let cache_service = Arc::new(CacheService::new(cache_manager));

    // 创建验证服务
    let validation_service = Arc::new(ValidationService::new());

    // 创建模型提供商服务
    Arc::new(ModelProviderService::new(
        repository,
        cache_service,
        validation_service,
    ))
}

/// 基准测试：创建模型提供商
fn benchmark_create_provider(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();
    let service = rt.block_on(setup_test_environment());

    let mut group = c.benchmark_group("create_provider");
    group.measurement_time(Duration::from_secs(10));

    // 测试单个创建操作
    group.bench_function("single_create", |b| {
        let mut counter = 0;
        b.iter(|| {
            rt.block_on(async {
                counter += 1;
                let request = create_test_provider_request(&format!("provider_{}", counter));
                black_box(service.create(request).await)
            })
        });
    });

    // 测试批量创建操作
    for batch_size in [10, 50, 100].iter() {
        group.bench_with_input(
            BenchmarkId::new("batch_create", batch_size),
            batch_size,
            |b, &batch_size| {
                b.iter(|| {
                    rt.block_on(async {
                        let mut handles = Vec::new();
                        for i in 0..batch_size {
                            let service = service.clone();
                            let request = create_test_provider_request(&format!("batch_provider_{}_{}", batch_size, i));
                            let handle = tokio::spawn(async move {
                                service.create(request).await
                            });
                            handles.push(handle);
                        }

                        // 等待所有任务完成
                        for handle in handles {
                            let _ = black_box(handle.await);
                        }
                    })
                });
            },
        );
    }

    group.finish();
}

/// 基准测试：获取模型提供商
fn benchmark_get_provider(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();
    let service = rt.block_on(setup_test_environment());

    // 预先创建一些测试数据
    let provider_ids: Vec<String> = rt.block_on(async {
        let mut ids = Vec::new();
        for i in 0..100 {
            let request = create_test_provider_request(&format!("get_test_provider_{}", i));
            if let Ok(id) = service.create(request).await {
                ids.push(id);
            }
        }
        ids
    });

    let mut group = c.benchmark_group("get_provider");
    group.measurement_time(Duration::from_secs(10));

    // 测试单个获取操作（首次访问，无缓存）
    group.bench_function("single_get_no_cache", |b| {
        let mut index = 0;
        b.iter(|| {
            rt.block_on(async {
                // 清除缓存确保每次都是首次访问
                service.cache_service.clear_all().await;
                let id = &provider_ids[index % provider_ids.len()];
                index += 1;
                black_box(service.get_by_id(id).await)
            })
        });
    });

    // 测试单个获取操作（缓存命中）
    group.bench_function("single_get_cached", |b| {
        let mut index = 0;
        b.iter(|| {
            rt.block_on(async {
                let id = &provider_ids[index % provider_ids.len()];
                index += 1;
                black_box(service.get_by_id(id).await)
            })
        });
    });

    // 测试并发获取操作
    for concurrency in [10, 50, 100].iter() {
        group.bench_with_input(
            BenchmarkId::new("concurrent_get", concurrency),
            concurrency,
            |b, &concurrency| {
                b.iter(|| {
                    rt.block_on(async {
                        let mut handles = Vec::new();
                        for i in 0..concurrency {
                            let service = service.clone();
                            let id = provider_ids[i % provider_ids.len()].clone();
                            let handle = tokio::spawn(async move {
                                service.get_by_id(&id).await
                            });
                            handles.push(handle);
                        }

                        // 等待所有任务完成
                        for handle in handles {
                            let _ = black_box(handle.await);
                        }
                    })
                });
            },
        );
    }

    group.finish();
}

/// 基准测试：更新模型提供商
fn benchmark_update_provider(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();
    let service = rt.block_on(setup_test_environment());

    // 预先创建一些测试数据
    let provider_ids: Vec<String> = rt.block_on(async {
        let mut ids = Vec::new();
        for i in 0..50 {
            let request = create_test_provider_request(&format!("update_test_provider_{}", i));
            if let Ok(id) = service.create(request).await {
                ids.push(id);
            }
        }
        ids
    });

    let mut group = c.benchmark_group("update_provider");
    group.measurement_time(Duration::from_secs(10));

    // 测试单个更新操作
    group.bench_function("single_update", |b| {
        let mut index = 0;
        b.iter(|| {
            rt.block_on(async {
                let id = &provider_ids[index % provider_ids.len()];
                index += 1;
                let request = create_test_update_request();
                black_box(service.update(id, request).await)
            })
        });
    });

    // 测试并发更新操作
    for concurrency in [5, 10, 20].iter() {
        group.bench_with_input(
            BenchmarkId::new("concurrent_update", concurrency),
            concurrency,
            |b, &concurrency| {
                b.iter(|| {
                    rt.block_on(async {
                        let mut handles = Vec::new();
                        for i in 0..concurrency {
                            let service = service.clone();
                            let id = provider_ids[i % provider_ids.len()].clone();
                            let request = create_test_update_request();
                            let handle = tokio::spawn(async move {
                                service.update(&id, request).await
                            });
                            handles.push(handle);
                        }

                        // 等待所有任务完成
                        for handle in handles {
                            let _ = black_box(handle.await);
                        }
                    })
                });
            },
        );
    }

    group.finish();
}

/// 基准测试：删除模型提供商
fn benchmark_delete_provider(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();
    let service = rt.block_on(setup_test_environment());

    let mut group = c.benchmark_group("delete_provider");
    group.measurement_time(Duration::from_secs(10));

    // 测试单个删除操作
    group.bench_function("single_delete", |b| {
        let mut counter = 0;
        b.iter(|| {
            rt.block_on(async {
                // 每次测试前创建一个新的提供商
                counter += 1;
                let request = create_test_provider_request(&format!("delete_test_provider_{}", counter));
                if let Ok(id) = service.create(request).await {
                    black_box(service.delete(&id).await)
                } else {
                    Ok(())
                }
            })
        });
    });

    group.finish();
}

/// 基准测试：搜索模型提供商
fn benchmark_search_provider(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();
    let service = rt.block_on(setup_test_environment());

    // 预先创建大量测试数据
    rt.block_on(async {
        for i in 0..200 {
            let request = create_test_provider_request(&format!("search_test_provider_{}", i));
            let _ = service.create(request).await;
        }
    });

    let mut group = c.benchmark_group("search_provider");
    group.measurement_time(Duration::from_secs(10));

    // 测试基础搜索
    group.bench_function("basic_search", |b| {
        b.iter(|| {
            rt.block_on(async {
                let query = SearchQuery {
                    query: Some("search_test".to_string()),
                    enabled: None,
                    builtin: None,
                    api_type: None,
                    from: 0,
                    size: 20,
                    sort: None,
                };
                black_box(service.search(query).await)
            })
        });
    });

    // 测试分页搜索
    for page_size in [10, 50, 100].iter() {
        group.bench_with_input(
            BenchmarkId::new("paginated_search", page_size),
            page_size,
            |b, &page_size| {
                b.iter(|| {
                    rt.block_on(async {
                        let query = SearchQuery {
                            query: None,
                            enabled: None,
                            builtin: None,
                            api_type: None,
                            from: 0,
                            size: page_size,
                            sort: None,
                        };
                        black_box(service.search(query).await)
                    })
                });
            },
        );
    }

    group.finish();
}

criterion_group!(
    benches,
    benchmark_create_provider,
    benchmark_get_provider,
    benchmark_update_provider,
    benchmark_delete_provider,
    benchmark_search_provider
);
criterion_main!(benches);
