warning: unused import: `response::IntoResponse`
 --> src/handlers/account_handler.rs:1:46
  |
1 | use axum::{extract::State, http::StatusCode, response::IntoResponse, Extension, Json};
  |                                              ^^^^^^^^^^^^^^^^^^^^^^
  |
  = note: `#[warn(unused_imports)]` on by default

warning: unused import: `crate::error::result::AxumResult`
  --> src/handlers/account_handler.rs:12:5
   |
12 | use crate::error::result::AxumResult;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused imports: `HeaderMap`, `Router`, `body::Bytes`, `get`, and `post`
 --> src/handlers/connector_handler.rs:4:5
  |
4 |     body::Bytes,
  |     ^^^^^^^^^^^
5 |     extract::{Query, State},
6 |     http::{HeaderMap, StatusCode},
  |            ^^^^^^^^^
7 |     response::<PERSON><PERSON>,
8 |     routing::{get, post},
  |               ^^^  ^^^^
9 |     Router,
  |     ^^^^^^

warning: unused imports: `Deserialize` and `Serialize`
  --> src/handlers/connector_handler.rs:11:13
   |
11 | use serde::{Deserialize, Serialize};
   |             ^^^^^^^^^^^  ^^^^^^^^^

warning: unused import: `crate::error::error::CocoError`
 --> src/handlers/datasource_handler.rs:2:5
  |
2 | use crate::error::error::CocoError;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `ModelSettings`
 --> src/handlers/field_filter.rs:6:65
  |
6 | use crate::models::model_provider::{ModelConfig, ModelProvider, ModelSettings};
  |                                                                 ^^^^^^^^^^^^^

warning: unused import: `crate::app_state::AppState`
 --> src/handlers/info_handler.rs:1:5
  |
1 | use crate::app_state::AppState;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `Serialize`
 --> src/handlers/model_provider_handler.rs:8:26
  |
8 | use serde::{Deserialize, Serialize};
  |                          ^^^^^^^^^

warning: unused import: `Deserialize`
 --> src/handlers/sso_handler.rs:6:13
  |
6 | use serde::{Deserialize, Serialize};
  |             ^^^^^^^^^^^

warning: unused imports: `HeaderValue` and `extract::Path`
 --> src/handlers/static_handler.rs:3:5
  |
3 |     extract::Path,
  |     ^^^^^^^^^^^^^
4 |     http::{header, HeaderMap, HeaderValue, Request},
  |                               ^^^^^^^^^^^

warning: unused import: `AuthType`
  --> src/middleware/auth_middleware.rs:16:23
   |
16 |         user_claims::{AuthType, UserContext},
   |                       ^^^^^^^^

warning: unused import: `info`
 --> src/monitoring/performance_metrics.rs:6:22
  |
6 | use tracing::{debug, info, warn};
  |                      ^^^^

warning: unused import: `warn`
 --> src/repositories/cache_manager.rs:6:28
  |
6 | use tracing::{debug, info, warn};
  |                            ^^^^

warning: unused import: `uuid::Uuid`
  --> src/repositories/datasource_repo.rs:12:5
   |
12 | use uuid::Uuid;
   |     ^^^^^^^^^^

warning: unused import: `warn`
 --> src/repositories/model_provider_repo.rs:7:28
  |
7 | use tracing::{error, info, warn};
  |                            ^^^^

warning: unused import: `builtin_provider_config::BuiltinProviderConfigManager`
  --> src/services/config_reload_service.rs:14:9
   |
14 |         builtin_provider_config::BuiltinProviderConfigManager, config_manager::ConfigManager,
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `SearchParams`
  --> src/services/model_provider_service.rs:12:9
   |
12 |         SearchParams,
   |         ^^^^^^^^^^^^

warning: unused variable: `config_manager`
  --> src/handlers/connector_handler.rs:19:11
   |
19 |     State(config_manager): State<Arc<ConfigManager>>,
   |           ^^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_config_manager`
   |
   = note: `#[warn(unused_variables)]` on by default

warning: unused variable: `config_manager`
  --> src/handlers/connector_handler.rs:90:11
   |
90 |     State(config_manager): State<Arc<ConfigManager>>,
   |           ^^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_config_manager`

warning: unused variable: `config_manager`
   --> src/handlers/datasource_handler.rs:166:11
    |
166 |     State(config_manager): State<Arc<ConfigManager>>,
    |           ^^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_config_manager`

warning: unused variable: `config_manager`
   --> src/handlers/datasource_handler.rs:238:11
    |
238 |     State(config_manager): State<Arc<ConfigManager>>,
    |           ^^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_config_manager`

warning: unused variable: `config_manager`
  --> src/handlers/model_provider_handler.rs:51:11
   |
51 |     State(config_manager): State<Arc<ConfigManager>>,
   |           ^^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_config_manager`

warning: unused variable: `config_manager`
  --> src/handlers/model_provider_handler.rs:79:11
   |
79 |     State(config_manager): State<Arc<ConfigManager>>,
   |           ^^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_config_manager`

warning: unused variable: `config_manager`
   --> src/handlers/model_provider_handler.rs:111:11
    |
111 |     State(config_manager): State<Arc<ConfigManager>>,
    |           ^^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_config_manager`

warning: unused variable: `config_manager`
   --> src/handlers/model_provider_handler.rs:140:11
    |
140 |     State(config_manager): State<Arc<ConfigManager>>,
    |           ^^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_config_manager`

warning: unused variable: `config_manager`
   --> src/handlers/model_provider_handler.rs:277:11
    |
277 |     State(config_manager): State<Arc<ConfigManager>>,
    |           ^^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_config_manager`

warning: unused variable: `config_manager`
   --> src/handlers/model_provider_handler.rs:344:11
    |
344 |     State(config_manager): State<Arc<ConfigManager>>,
    |           ^^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_config_manager`

warning: unused variable: `config_manager`
  --> src/handlers/sso_handler.rs:59:11
   |
59 |     State(config_manager): State<Arc<ConfigManager>>,
   |           ^^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_config_manager`

warning: unused variable: `product`
  --> src/handlers/sso_handler.rs:67:9
   |
67 |     let product = params.product.unwrap_or_else(|| "coco".to_string());
   |         ^^^^^^^ help: if this is intentional, prefix it with an underscore: `_product`

warning: unused variable: `datasource`
  --> src/repositories/datasource_repo.rs:58:32
   |
58 |     pub async fn create(&self, datasource: &DataSource) -> Result<String, RepositoryError> {
   |                                ^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_datasource`

warning: unused variable: `id`
   --> src/services/performance_optimization.rs:347:36
    |
347 |     async fn get_from_cache(&self, id: &str) -> Option<ModelProvider> {
    |                                    ^^ help: if this is intentional, prefix it with an underscore: `_id`

warning: unused variable: `now`
   --> src/auth/jwt_cache.rs:136:13
    |
136 |         let now = Instant::now();
    |             ^^^ help: if this is intentional, prefix it with an underscore: `_now`

warning: unused variable: `i`
   --> src/repositories/model_provider_repo.rs:241:20
    |
241 |             .map(|(i, provider)| SearchHit {
    |                    ^ help: if this is intentional, prefix it with an underscore: `_i`

warning: unused variable: `is_running_clone`
  --> src/services/file_watcher.rs:60:13
   |
60 |         let is_running_clone = is_running.clone();
   |             ^^^^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_is_running_clone`

warning: field `last_modified` is never read
  --> src/config/config_manager.rs:29:5
   |
18 | pub struct ConfigManager {
   |            ------------- field in this struct
...
29 |     last_modified: Option<SystemTime>,
   |     ^^^^^^^^^^^^^
   |
   = note: `ConfigManager` has a derived impl for the trait `Clone`, but this is intentionally ignored during dead code analysis
   = note: `#[warn(dead_code)]` on by default

warning: field `config` is never read
  --> src/database/client.rs:13:5
   |
9  | pub struct SurrealDBClient {
   |            --------------- field in this struct
...
13 |     config: DatabaseConfig,
   |     ^^^^^^
   |
   = note: `SurrealDBClient` has a derived impl for the trait `Clone`, but this is intentionally ignored during dead code analysis

warning: method `initialize_schema` is never used
  --> src/database/client.rs:44:14
   |
16 | impl SurrealDBClient {
   | -------------------- method in this implementation
...
44 |     async fn initialize_schema(&self) -> Result<()> {
   |              ^^^^^^^^^^^^^^^^^

warning: constant `DEFAULT_USER_PASSWORD_KEY` is never used
  --> src/handlers/account_handler.rs:68:7
   |
68 | const DEFAULT_USER_PASSWORD_KEY: &str = "default_user_password";
   |       ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: field `service` is never read
  --> src/handlers/model_provider_handler.rs:34:5
   |
33 | pub struct ModelProviderHandler {
   |            -------------------- field in this struct
34 |     service: Arc<ModelProviderService>,
   |     ^^^^^^^

warning: fields `client` and `config_manager` are never read
  --> src/health/health_checker.rs:9:5
   |
8  | pub struct HealthChecker {
   |            ------------- fields in this struct
9  |     client: reqwest::Client,
   |     ^^^^^^
10 |     config_manager: ConfigManager,
   |     ^^^^^^^^^^^^^^

warning: methods `check_elasticsearch`, `check_database`, and `check_api_service` are never used
   --> src/health/health_checker.rs:61:14
    |
13  | impl HealthChecker {
    | ------------------ methods in this implementation
...
61  |     async fn check_elasticsearch(&self) -> String {
    |              ^^^^^^^^^^^^^^^^^^^
...
111 |     async fn check_database(&self) -> String {
    |              ^^^^^^^^^^^^^^
...
195 |     async fn check_api_service(&self) -> String {
    |              ^^^^^^^^^^^^^^^^^

warning: method `build_thing_id` is never used
   --> src/repositories/model_provider_repo.rs:297:8
    |
287 | impl SurrealModelProviderRepository {
    | ----------------------------------- method in this implementation
...
297 |     fn build_thing_id(&self, id: &str) -> Thing {
    |        ^^^^^^^^^^^^^^

warning: field `last_modified` is never read
  --> src/services/file_watcher.rs:38:5
   |
30 | pub struct FileWatcher {
   |            ----------- field in this struct
...
38 |     last_modified: Arc<std::sync::Mutex<Option<SystemTime>>>,
   |     ^^^^^^^^^^^^^

warning: fields `db`, `config_manager`, and `model_provider_repo` are never read
  --> src/services/initialization_service.rs:70:5
   |
68 | pub struct InitializationService {
   |            --------------------- fields in this struct
69 |     /// 数据库连接
70 |     db: Arc<Surreal<Client>>,
   |     ^^
71 |     /// 配置管理器
72 |     config_manager: Arc<ConfigManager>,
   |     ^^^^^^^^^^^^^^
73 |     /// 模型提供商仓储
74 |     model_provider_repo: Arc<dyn ModelProviderRepository>,
   |     ^^^^^^^^^^^^^^^^^^^
   |
   = note: `InitializationService` has a derived impl for the trait `Clone`, but this is intentionally ignored during dead code analysis

warning: field `validation_service` is never read
  --> src/services/model_provider_service.rs:29:5
   |
23 | pub struct ModelProviderService {
   |            -------------------- field in this struct
...
29 |     validation_service: Arc<ValidationService>,
   |     ^^^^^^^^^^^^^^^^^^

warning: field `cache_service` is never read
  --> src/services/performance_optimization.rs:25:5
   |
21 | pub struct PerformanceOptimizationService {
   |            ------------------------------ field in this struct
...
25 |     cache_service: Arc<CacheService>,
   |     ^^^^^^^^^^^^^

warning: field `model_provider_repo` is never read
  --> src/services/system_validator.rs:21:5
   |
15 | pub struct SystemValidator {
   |            --------------- field in this struct
...
21 |     model_provider_repo: Arc<dyn ModelProviderRepository>,
   |     ^^^^^^^^^^^^^^^^^^^
   |
   = note: `SystemValidator` has a derived impl for the trait `Clone`, but this is intentionally ignored during dead code analysis

warning: fields `id`, `name`, and `enabled` are never read
  --> src/services/validation_service.rs:81:5
   |
79 | struct ConnectorInfo {
   |        ------------- fields in this struct
80 |     /// 连接器ID
81 |     id: String,
   |     ^^
82 |     /// 连接器名称
83 |     name: String,
   |     ^^^^
84 |     /// 是否启用
85 |     enabled: bool,
   |     ^^^^^^^
   |
   = note: `ConnectorInfo` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis

warning: `coco-server` (lib) generated 48 warnings (run `cargo fix --lib -p coco-server` to apply 17 suggestions)
warning: unused import: `Client`
 --> src/bin/debug_connection.rs:2:26
  |
2 |     engine::remote::ws::{Client, Ws},
  |                          ^^^^^^
  |
  = note: `#[warn(unused_imports)]` on by default

warning: unused import: `Client`
 --> src/bin/init_database.rs:3:26
  |
3 |     engine::remote::ws::{Client, Ws},
  |                          ^^^^^^
  |
  = note: `#[warn(unused_imports)]` on by default

   Compiling coco-server v0.1.0 (/Users/<USER>/local_doc/l_dev/my/rust/aigui/coco-server/coco-server-rust)
warning: unused import: `Client`
 --> src/bin/simple_connection_test.rs:2:26
  |
2 |     engine::remote::ws::{Client, Ws},
  |                          ^^^^^^
  |
  = note: `#[warn(unused_imports)]` on by default

warning: `coco-server` (bin "debug_connection") generated 1 warning (run `cargo fix --bin "debug_connection"` to apply 1 suggestion)
warning: `coco-server` (bin "init_database") generated 1 warning (run `cargo fix --bin "init_database"` to apply 1 suggestion)
warning: `coco-server` (bin "simple_connection_test") generated 1 warning (run `cargo fix --bin "simple_connection_test"` to apply 1 suggestion)
error[E0432]: unresolved import `coco_server::database::client::DatabaseClient`
  --> benches/database_benchmark.rs:12:5
   |
12 |     database::client::DatabaseClient,
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ no `DatabaseClient` in `database::client`

warning: unused import: `cache_manager::CacheManager`
  --> benches/database_benchmark.rs:10:9
   |
10 |         cache_manager::CacheManager,
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = note: `#[warn(unused_imports)]` on by default

error[E0308]: mismatched types
  --> benches/database_benchmark.rs:22:19
   |
22 |         base_url: Some("https://api.openai.com/v1".to_string()),
   |                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ expected `String`, found `Option<String>`
   |
   = note: expected struct `String`
                found enum `Option<String>`

error[E0308]: mismatched types
  --> benches/database_benchmark.rs:23:22
   |
23 |         description: Some(format!("测试提供商 {}", name)),
   |                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ expected `String`, found `Option<String>`
   |
   = note: expected struct `String`
                found enum `Option<String>`

error[E0063]: missing field `icon` in initializer of `ModelProvider`
  --> benches/database_benchmark.rs:17:5
   |
17 |     ModelProvider {
   |     ^^^^^^^^^^^^^ missing `icon`

error[E0599]: no method named `find_by_id` found for struct `Arc<SurrealModelProviderRepository>` in the current scope
   --> benches/database_benchmark.rs:168:38
    |
168 |                 black_box(repository.find_by_id(id).await)
    |                                      ^^^^^^^^^^
    |
help: there is a method `find_by_name` with a similar name
    |
168 -                 black_box(repository.find_by_id(id).await)
168 +                 black_box(repository.find_by_name(id).await)
    |

error[E0599]: no method named `find_by_id` found for struct `Arc<SurrealModelProviderRepository>` in the current scope
   --> benches/database_benchmark.rs:198:44
    |
198 | ...                   repository.find_by_id(&id).await
    |                                  ^^^^^^^^^^
    |
help: there is a method `find_by_name` with a similar name
    |
198 -                                 repository.find_by_id(&id).await
198 +                                 repository.find_by_name(&id).await
    |

error[E0560]: struct `coco_server::repositories::SearchQuery` has no field named `query`
   --> benches/database_benchmark.rs:332:21
    |
332 |                     query: Some("search_perf_test".to_string()),
    |                     ^^^^^ `coco_server::repositories::SearchQuery` does not have this field
    |
    = note: available fields are: `q`, `filters`

error[E0560]: struct `coco_server::repositories::SearchQuery` has no field named `enabled`
   --> benches/database_benchmark.rs:333:21
    |
333 |                     enabled: None,
    |                     ^^^^^^^ `coco_server::repositories::SearchQuery` does not have this field
    |
    = note: available fields are: `q`, `filters`

error[E0560]: struct `coco_server::repositories::SearchQuery` has no field named `builtin`
   --> benches/database_benchmark.rs:334:21
    |
334 |                     builtin: None,
    |                     ^^^^^^^ `coco_server::repositories::SearchQuery` does not have this field
    |
    = note: available fields are: `q`, `filters`

error[E0560]: struct `coco_server::repositories::SearchQuery` has no field named `api_type`
   --> benches/database_benchmark.rs:335:21
    |
335 |                     api_type: None,
    |                     ^^^^^^^^ `coco_server::repositories::SearchQuery` does not have this field
    |
    = note: available fields are: `q`, `filters`

error[E0308]: mismatched types
   --> benches/database_benchmark.rs:336:27
    |
336 |                     from: 0,
    |                           ^ expected `Option<usize>`, found integer
    |
    = note: expected enum `Option<usize>`
               found type `{integer}`
help: try wrapping the expression in `Some`
    |
336 |                     from: Some(0),
    |                           +++++ +

error[E0308]: mismatched types
   --> benches/database_benchmark.rs:337:27
    |
337 |                     size: 20,
    |                           ^^ expected `Option<usize>`, found integer
    |
    = note: expected enum `Option<usize>`
               found type `{integer}`
help: try wrapping the expression in `Some`
    |
337 |                     size: Some(20),
    |                           +++++  +

error[E0560]: struct `coco_server::repositories::SearchQuery` has no field named `query`
   --> benches/database_benchmark.rs:354:29
    |
354 | ...                   query: None,
    |                       ^^^^^ `coco_server::repositories::SearchQuery` does not have this field
    |
    = note: available fields are: `q`, `filters`

error[E0560]: struct `coco_server::repositories::SearchQuery` has no field named `enabled`
   --> benches/database_benchmark.rs:355:29
    |
355 | ...                   enabled: None,
    |                       ^^^^^^^ `coco_server::repositories::SearchQuery` does not have this field
    |
    = note: available fields are: `q`, `filters`

error[E0560]: struct `coco_server::repositories::SearchQuery` has no field named `builtin`
   --> benches/database_benchmark.rs:356:29
    |
356 | ...                   builtin: None,
    |                       ^^^^^^^ `coco_server::repositories::SearchQuery` does not have this field
    |
    = note: available fields are: `q`, `filters`

error[E0560]: struct `coco_server::repositories::SearchQuery` has no field named `api_type`
   --> benches/database_benchmark.rs:357:29
    |
357 | ...                   api_type: None,
    |                       ^^^^^^^^ `coco_server::repositories::SearchQuery` does not have this field
    |
    = note: available fields are: `q`, `filters`

error[E0308]: mismatched types
   --> benches/database_benchmark.rs:358:35
    |
358 | ...                   from: 0,
    |                             ^ expected `Option<usize>`, found integer
    |
    = note: expected enum `Option<usize>`
               found type `{integer}`
help: try wrapping the expression in `Some`
    |
358 |                             from: Some(0),
    |                                   +++++ +

error[E0308]: mismatched types
   --> benches/database_benchmark.rs:359:35
    |
359 | ...                   size: page_size,
    |                             ^^^^^^^^^ expected `Option<usize>`, found integer
    |
    = note: expected enum `Option<usize>`
               found type `{integer}`
help: try wrapping the expression in `Some`
    |
359 |                             size: Some(page_size),
    |                                   +++++         +

error[E0560]: struct `coco_server::repositories::SearchQuery` has no field named `query`
   --> benches/database_benchmark.rs:374:21
    |
374 |                     query: None,
    |                     ^^^^^ `coco_server::repositories::SearchQuery` does not have this field
    |
    = note: available fields are: `q`, `filters`

error[E0560]: struct `coco_server::repositories::SearchQuery` has no field named `enabled`
   --> benches/database_benchmark.rs:375:21
    |
375 |                     enabled: Some(true),
    |                     ^^^^^^^ `coco_server::repositories::SearchQuery` does not have this field
    |
    = note: available fields are: `q`, `filters`

error[E0560]: struct `coco_server::repositories::SearchQuery` has no field named `builtin`
   --> benches/database_benchmark.rs:376:21
    |
376 |                     builtin: Some(false),
    |                     ^^^^^^^ `coco_server::repositories::SearchQuery` does not have this field
    |
    = note: available fields are: `q`, `filters`

error[E0560]: struct `coco_server::repositories::SearchQuery` has no field named `api_type`
   --> benches/database_benchmark.rs:377:21
    |
377 |                     api_type: Some("openai".to_string()),
    |                     ^^^^^^^^ `coco_server::repositories::SearchQuery` does not have this field
    |
    = note: available fields are: `q`, `filters`

error[E0308]: mismatched types
   --> benches/database_benchmark.rs:378:27
    |
378 |                     from: 0,
    |                           ^ expected `Option<usize>`, found integer
    |
    = note: expected enum `Option<usize>`
               found type `{integer}`
help: try wrapping the expression in `Some`
    |
378 |                     from: Some(0),
    |                           +++++ +

error[E0308]: mismatched types
   --> benches/database_benchmark.rs:379:27
    |
379 |                     size: 50,
    |                           ^^ expected `Option<usize>`, found integer
    |
    = note: expected enum `Option<usize>`
               found type `{integer}`
help: try wrapping the expression in `Some`
    |
379 |                     size: Some(50),
    |                           +++++  +

Some errors have detailed explanations: E0063, E0308, E0432, E0560, E0599.
For more information about an error, try `rustc --explain E0063`.
warning: `coco-server` (bench "database_benchmark") generated 1 warning
error: could not compile `coco-server` (bench "database_benchmark") due to 24 previous errors; 1 warning emitted
warning: build failed, waiting for other jobs to finish...
error[E0433]: failed to resolve: unresolved import
  --> src/services/performance_optimization.rs:13:5
   |
13 |     monitoring::performance_metrics::{MetricType, PerformanceMetrics},
   |     ^^^^^^^^^^
   |     |
   |     unresolved import
   |     help: a similar path exists: `coco_server::monitoring`

warning: unused import: `auth_manager::*`
 --> src/auth/mod.rs:8:9
  |
8 | pub use auth_manager::*;
  |         ^^^^^^^^^^^^^^^
  |
  = note: `#[warn(unused_imports)]` on by default

warning: unused import: `jwt_cache::*`
 --> src/auth/mod.rs:9:9
  |
9 | pub use jwt_cache::*;
  |         ^^^^^^^^^^^^

warning: unused import: `permission_checker::*`
  --> src/auth/mod.rs:10:9
   |
10 | pub use permission_checker::*;
   |         ^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `token_blacklist::*`
  --> src/auth/mod.rs:11:9
   |
11 | pub use token_blacklist::*;
   |         ^^^^^^^^^^^^^^^^^^

warning: unused import: `user_claims::*`
  --> src/auth/mod.rs:12:9
   |
12 | pub use user_claims::*;
   |         ^^^^^^^^^^^^^^

warning: unused import: `response::IntoResponse`
 --> src/handlers/account_handler.rs:1:46
  |
1 | use axum::{extract::State, http::StatusCode, response::IntoResponse, Extension, Json};
  |                                              ^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `access_token::*`
  --> src/models/mod.rs:21:9
   |
21 | pub use access_token::*;
   |         ^^^^^^^^^^^^^^^

warning: unused import: `document::*`
  --> src/models/mod.rs:24:9
   |
24 | pub use document::*;
   |         ^^^^^^^^^^^

warning: unused import: `model_provider::*`
  --> src/models/mod.rs:26:9
   |
26 | pub use model_provider::*;
   |         ^^^^^^^^^^^^^^^^^

warning: unused import: `validation::*`
  --> src/models/mod.rs:27:9
   |
27 | pub use validation::*;
   |         ^^^^^^^^^^^^^

warning: unused import: `cache_manager::*`
  --> src/repositories/mod.rs:22:9
   |
22 | pub use cache_manager::*;
   |         ^^^^^^^^^^^^^^^^

warning: unused import: `datasource_repo::*`
  --> src/repositories/mod.rs:23:9
   |
23 | pub use datasource_repo::*;
   |         ^^^^^^^^^^^^^^^^^^

warning: unused import: `model_provider_repo::*`
  --> src/repositories/mod.rs:24:9
   |
24 | pub use model_provider_repo::*;
   |         ^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `token_repository::*`
  --> src/repositories/mod.rs:27:9
   |
27 | pub use token_repository::*;
   |         ^^^^^^^^^^^^^^^^^^^

warning: unused import: `builtin_protection::*`
  --> src/services/mod.rs:37:9
   |
37 | pub use builtin_protection::*;
   |         ^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `cache_service::*`
  --> src/services/mod.rs:38:9
   |
38 | pub use cache_service::*;
   |         ^^^^^^^^^^^^^^^^

warning: unused import: `database_setup::*`
  --> src/services/mod.rs:40:9
   |
40 | pub use database_setup::*;
   |         ^^^^^^^^^^^^^^^^^

warning: unused import: `datasource_service::*`
  --> src/services/mod.rs:41:9
   |
41 | pub use datasource_service::*;
   |         ^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `file_watcher::*`
  --> src/services/mod.rs:42:9
   |
42 | pub use file_watcher::*;
   |         ^^^^^^^^^^^^^^^

warning: unused import: `filter_service::*`
  --> src/services/mod.rs:43:9
   |
43 | pub use filter_service::*;
   |         ^^^^^^^^^^^^^^^^^

warning: unused import: `import_strategy::*`
  --> src/services/mod.rs:44:9
   |
44 | pub use import_strategy::*;
   |         ^^^^^^^^^^^^^^^^^^

warning: unused import: `initialization_service::*`
  --> src/services/mod.rs:45:9
   |
45 | pub use initialization_service::*;
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `model_provider_service::*`
  --> src/services/mod.rs:46:9
   |
46 | pub use model_provider_service::*;
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `pagination::*`
  --> src/services/mod.rs:47:9
   |
47 | pub use pagination::*;
   |         ^^^^^^^^^^^^^

warning: unused import: `provider_import_service::*`
  --> src/services/mod.rs:48:9
   |
48 | pub use provider_import_service::*;
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `system_validator::*`
  --> src/services/mod.rs:49:9
   |
49 | pub use system_validator::*;
   |         ^^^^^^^^^^^^^^^^^^^

warning: unused import: `token_service::*`
  --> src/services/mod.rs:50:9
   |
50 | pub use token_service::*;
   |         ^^^^^^^^^^^^^^^^

warning: unused import: `validation_service::*`
  --> src/services/mod.rs:51:9
   |
51 | pub use validation_service::*;
   |         ^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `DB`
   --> src/main.rs:279:62
    |
279 |         database::{init_database, schema::SchemaInitializer, DB},
    |                                                              ^^

warning: unused variable: `token_blacklist`
   --> src/main.rs:235:9
    |
235 |     let token_blacklist = Arc::new(TokenBlacklist::new());
    |         ^^^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_token_blacklist`

For more information about this error, try `rustc --explain E0433`.
warning: `coco-server` (bin "coco-server") generated 62 warnings (32 duplicates)
error: could not compile `coco-server` (bin "coco-server") due to 1 previous error; 62 warnings emitted
