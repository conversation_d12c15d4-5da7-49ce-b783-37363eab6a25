# 配置重载功能用户指南

## 概述

配置重载功能允许您在不重启服务的情况下动态更新 coco-server 的配置。本指南将帮助您了解如何使用这个功能。

## 功能特性

### 🔄 重载方式
- **手动重载**: 通过 API 调用主动触发配置重载
- **热重载**: 自动监控配置文件变化并重载

### 🛡️ 安全保障
- **配置验证**: 重载前验证配置文件的正确性
- **自动备份**: 成功重载后自动备份配置
- **失败回滚**: 验证失败时自动恢复到上一个有效配置

### 📊 监控统计
- **重载状态**: 实时查看重载状态
- **统计信息**: 详细的重载统计数据
- **性能指标**: 重载耗时和成功率

## 快速开始

### 1. 基本使用

```rust
use coco_server::services::ConfigReloadService;
use std::sync::Arc;
use std::path::PathBuf;

// 创建配置重载服务
let config_manager = Arc::new(ConfigManager::new()?);
let config_path = PathBuf::from("config.yaml");
let service = ConfigReloadService::new(config_manager, &config_path)?;

// 手动重载配置
match service.reload_config().await {
    Ok(_) => println!("✅ 配置重载成功"),
    Err(e) => println!("❌ 配置重载失败: {}", e),
}
```

### 2. 启用热重载

```rust
// 启用热重载功能
service.start_hot_reload().await?;
println!("🔥 热重载已启用，配置文件变化时将自动重载");

// 停止热重载
service.stop_hot_reload().await?;
println!("⏹️ 热重载已停止");
```

### 3. 查询状态和统计

```rust
// 查询当前状态
let status = service.get_status();
match status {
    ReloadStatus::Idle => println!("💤 空闲状态"),
    ReloadStatus::Reloading => println!("🔄 重载中..."),
    ReloadStatus::Success(msg) => println!("✅ 重载成功: {}", msg),
    ReloadStatus::Failed(msg) => println!("❌ 重载失败: {}", msg),
    ReloadStatus::RolledBack(msg) => println!("🔙 已回滚: {}", msg),
}

// 查询统计信息
let stats = service.get_stats();
println!("📊 重载统计:");
println!("  总重载次数: {}", stats.total_reloads);
println!("  成功次数: {}", stats.successful_reloads);
println!("  失败次数: {}", stats.failed_reloads);
println!("  回滚次数: {}", stats.rollback_count);
println!("  平均耗时: {:.2}ms", stats.average_reload_time_ms);
```

## 配置文件要求

### 1. 文件格式
- 支持 YAML 格式配置文件
- 必须符合 coco-server 的配置结构

### 2. 配置示例

```yaml
# config.yaml
api_binding: "0.0.0.0:2900"
database:
  host: "127.0.0.1"
  port: 8000
  username: "root"
  password: "root"
  
logging:
  level: "info"
  file: "logs/coco-server.log"
  
features:
  hot_reload: true
  backup_count: 5
```

### 3. 配置验证

重载前会进行以下验证：
1. **语法验证**: 检查 YAML 格式是否正确
2. **结构验证**: 验证配置字段是否完整
3. **业务验证**: 检查配置值是否合理

## 使用场景

### 1. 开发环境

```rust
// 开发时启用热重载，方便调试
async fn setup_dev_environment() -> Result<()> {
    let service = ConfigReloadService::new(config_manager, &config_path)?;
    
    // 启用热重载
    service.start_hot_reload().await?;
    
    println!("🚀 开发环境已启动，配置文件变化将自动重载");
    Ok(())
}
```

### 2. 生产环境

```rust
// 生产环境建议使用手动重载
async fn production_config_update() -> Result<()> {
    let service = ConfigReloadService::new(config_manager, &config_path)?;
    
    // 检查当前状态
    let status = service.get_status();
    if matches!(status, ReloadStatus::Reloading) {
        return Err("配置正在重载中，请稍后再试".into());
    }
    
    // 执行重载
    match service.reload_config().await {
        Ok(_) => {
            println!("✅ 生产环境配置更新成功");
            
            // 记录统计信息
            let stats = service.get_stats();
            println!("📊 重载耗时: {:.2}ms", stats.average_reload_time_ms);
        }
        Err(e) => {
            println!("❌ 配置更新失败: {}", e);
            
            // 检查是否已回滚
            let status = service.get_status();
            if matches!(status, ReloadStatus::RolledBack(_)) {
                println!("🔙 已自动回滚到上一个有效配置");
            }
        }
    }
    
    Ok(())
}
```

### 3. 批量配置更新

```rust
async fn batch_config_update(configs: Vec<PathBuf>) -> Result<()> {
    for config_path in configs {
        let service = ConfigReloadService::new(config_manager.clone(), &config_path)?;
        
        println!("🔄 正在更新配置: {:?}", config_path);
        
        match service.reload_config().await {
            Ok(_) => println!("✅ 配置更新成功: {:?}", config_path),
            Err(e) => {
                println!("❌ 配置更新失败: {:?}, 错误: {}", config_path, e);
                // 继续处理下一个配置文件
            }
        }
    }
    
    Ok(())
}
```

## 错误处理

### 1. 常见错误

| 错误类型 | 原因 | 解决方案 |
|---------|------|----------|
| 文件不存在 | 配置文件路径错误 | 检查文件路径是否正确 |
| 格式错误 | YAML 语法错误 | 使用 YAML 验证工具检查语法 |
| 验证失败 | 配置内容不符合要求 | 检查配置字段和值是否正确 |
| 权限错误 | 文件读写权限不足 | 检查文件权限设置 |

### 2. 错误恢复

```rust
async fn robust_config_reload(service: &ConfigReloadService) -> Result<()> {
    // 重试机制
    for attempt in 1..=3 {
        match service.reload_config().await {
            Ok(_) => {
                println!("✅ 配置重载成功 (第{}次尝试)", attempt);
                return Ok(());
            }
            Err(e) => {
                println!("❌ 配置重载失败 (第{}次尝试): {}", attempt, e);
                
                if attempt < 3 {
                    println!("⏳ 等待5秒后重试...");
                    tokio::time::sleep(Duration::from_secs(5)).await;
                } else {
                    println!("💥 重载失败，已达到最大重试次数");
                    
                    // 检查回滚状态
                    let status = service.get_status();
                    if matches!(status, ReloadStatus::RolledBack(_)) {
                        println!("🔙 系统已自动回滚到安全状态");
                    }
                    
                    return Err(e);
                }
            }
        }
    }
    
    Ok(())
}
```

## 最佳实践

### 1. 配置管理

```rust
// ✅ 推荐：使用版本控制管理配置
async fn managed_config_update() -> Result<()> {
    // 1. 备份当前配置
    let backup_path = format!("config.yaml.backup.{}", chrono::Utc::now().timestamp());
    std::fs::copy("config.yaml", &backup_path)?;
    
    // 2. 应用新配置
    let service = ConfigReloadService::new(config_manager, &PathBuf::from("config.yaml"))?;
    
    match service.reload_config().await {
        Ok(_) => {
            println!("✅ 配置更新成功");
            // 可以删除备份文件
            std::fs::remove_file(&backup_path)?;
        }
        Err(e) => {
            println!("❌ 配置更新失败: {}", e);
            // 保留备份文件用于排查问题
            println!("💾 备份文件已保存: {}", backup_path);
        }
    }
    
    Ok(())
}
```

### 2. 监控集成

```rust
// ✅ 推荐：集成监控系统
async fn monitored_reload(service: &ConfigReloadService) -> Result<()> {
    let start_time = std::time::Instant::now();
    
    match service.reload_config().await {
        Ok(_) => {
            let duration = start_time.elapsed();
            
            // 发送成功指标
            metrics::counter!("config_reload_success").increment(1);
            metrics::histogram!("config_reload_duration").record(duration.as_millis() as f64);
            
            println!("✅ 配置重载成功，耗时: {:?}", duration);
        }
        Err(e) => {
            let duration = start_time.elapsed();
            
            // 发送失败指标
            metrics::counter!("config_reload_failure").increment(1);
            metrics::histogram!("config_reload_duration").record(duration.as_millis() as f64);
            
            // 发送告警
            alert::send_alert(&format!("配置重载失败: {}", e)).await?;
            
            println!("❌ 配置重载失败: {}", e);
        }
    }
    
    Ok(())
}
```

### 3. 性能优化

```rust
// ✅ 推荐：避免频繁重载
pub struct ThrottledReloadService {
    inner: ConfigReloadService,
    last_reload: Arc<Mutex<Option<Instant>>>,
    min_interval: Duration,
}

impl ThrottledReloadService {
    pub async fn reload_config(&self) -> Result<()> {
        let now = Instant::now();
        
        // 检查重载间隔
        {
            let mut last_reload = self.last_reload.lock().unwrap();
            if let Some(last_time) = *last_reload {
                if now.duration_since(last_time) < self.min_interval {
                    return Err("重载过于频繁，请稍后再试".into());
                }
            }
            *last_reload = Some(now);
        }
        
        self.inner.reload_config().await
    }
}
```

## 故障排查

### 1. 诊断命令

```rust
// 健康检查
async fn health_check(service: &ConfigReloadService) {
    let status = service.get_status();
    let stats = service.get_stats();
    
    println!("🏥 配置重载健康检查:");
    println!("  当前状态: {:?}", status);
    println!("  成功率: {:.1}%", 
        if stats.total_reloads > 0 {
            (stats.successful_reloads as f64 / stats.total_reloads as f64) * 100.0
        } else {
            100.0
        }
    );
    println!("  平均耗时: {:.2}ms", stats.average_reload_time_ms);
    
    if let Some(last_reload) = stats.last_reload_time {
        println!("  最后重载: {}", last_reload.format("%Y-%m-%d %H:%M:%S"));
    }
}
```

### 2. 日志分析

查看日志文件中的相关信息：
```
2025-08-04 13:29:46 INFO  开始配置重载
2025-08-04 13:29:46 INFO  配置验证通过
2025-08-04 13:29:46 INFO  配置重载成功，耗时: 45ms
```

### 3. 常见问题

**Q: 热重载不工作？**
A: 检查文件系统是否支持文件监控，确保配置文件路径正确。

**Q: 重载后配置没有生效？**
A: 检查配置管理器是否正确实现了重载逻辑。

**Q: 频繁重载导致性能问题？**
A: 使用节流机制限制重载频率，或者禁用热重载改用手动重载。

## 总结

配置重载功能为 coco-server 提供了灵活的配置管理能力。通过合理使用手动重载和热重载功能，结合适当的错误处理和监控，可以大大提升系统的运维效率和稳定性。

记住以下要点：
- 🔍 重载前先验证配置文件
- 💾 重要环境建议手动重载
- 📊 定期检查重载统计信息
- 🚨 集成监控和告警系统
- 🔄 合理设置重载频率限制
