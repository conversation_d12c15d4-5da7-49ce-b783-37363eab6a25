# 配置重载 API 参考文档

## 概述

本文档详细描述了配置重载功能的所有 API 接口，包括数据结构、方法签名、参数说明和使用示例。

## 核心类型

### ConfigReloadService

配置重载服务的主要接口。

```rust
pub struct ConfigReloadService {
    // 私有字段
}
```

#### 构造方法

##### `new`

创建新的配置重载服务实例。

```rust
pub fn new(
    config_manager: Arc<Mutex<ConfigManager>>,
    config_path: &Path
) -> Result<Self, CocoError>
```

**参数:**
- `config_manager`: 配置管理器的共享引用
- `config_path`: 配置文件路径

**返回值:**
- `Ok(ConfigReloadService)`: 成功创建的服务实例
- `Err(CocoError)`: 创建失败的错误信息

**示例:**
```rust
let config_manager = Arc::new(Mutex::new(ConfigManager::new()?));
let config_path = PathBuf::from("config.yaml");
let service = ConfigReloadService::new(config_manager, &config_path)?;
```

#### 重载方法

##### `reload_config`

手动触发配置重载。

```rust
pub async fn reload_config(&self) -> Result<(), CocoError>
```

**返回值:**
- `Ok(())`: 重载成功
- `Err(CocoError)`: 重载失败

**行为:**
1. 设置状态为 `Reloading`
2. 验证新配置文件
3. 重载配置管理器
4. 验证重载后的配置
5. 备份成功的配置
6. 更新状态和统计信息

**示例:**
```rust
match service.reload_config().await {
    Ok(_) => println!("配置重载成功"),
    Err(e) => println!("配置重载失败: {}", e),
}
```

#### 热重载方法

##### `start_hot_reload`

启动热重载功能，监控配置文件变化。

```rust
pub async fn start_hot_reload(&mut self) -> Result<(), CocoError>
```

**返回值:**
- `Ok(())`: 热重载启动成功
- `Err(CocoError)`: 启动失败

**注意:**
- 如果热重载已经启动，会返回错误
- 文件变化后会自动触发 `reload_config`

**示例:**
```rust
service.start_hot_reload().await?;
println!("热重载已启动");
```

##### `stop_hot_reload`

停止热重载功能。

```rust
pub async fn stop_hot_reload(&mut self) -> Result<(), CocoError>
```

**返回值:**
- `Ok(())`: 热重载停止成功
- `Err(CocoError)`: 停止失败

**示例:**
```rust
service.stop_hot_reload().await?;
println!("热重载已停止");
```

##### `is_hot_reload_enabled`

检查热重载是否已启用。

```rust
pub fn is_hot_reload_enabled(&self) -> bool
```

**返回值:**
- `true`: 热重载已启用
- `false`: 热重载未启用

**示例:**
```rust
if service.is_hot_reload_enabled() {
    println!("热重载正在运行");
} else {
    println!("热重载未启用");
}
```

#### 状态查询方法

##### `get_status`

获取当前重载状态。

```rust
pub fn get_status(&self) -> ReloadStatus
```

**返回值:**
- `ReloadStatus`: 当前状态枚举值

**示例:**
```rust
let status = service.get_status();
match status {
    ReloadStatus::Idle => println!("空闲状态"),
    ReloadStatus::Reloading => println!("重载中"),
    ReloadStatus::Success(msg) => println!("成功: {}", msg),
    ReloadStatus::Failed(msg) => println!("失败: {}", msg),
    ReloadStatus::RolledBack(msg) => println!("已回滚: {}", msg),
}
```

##### `get_stats`

获取重载统计信息。

```rust
pub fn get_stats(&self) -> ReloadStats
```

**返回值:**
- `ReloadStats`: 统计信息结构体

**示例:**
```rust
let stats = service.get_stats();
println!("总重载次数: {}", stats.total_reloads);
println!("成功次数: {}", stats.successful_reloads);
println!("平均耗时: {:.2}ms", stats.average_reload_time_ms);
```

## 数据结构

### ReloadStatus

重载状态枚举。

```rust
#[derive(Debug, Clone, PartialEq)]
pub enum ReloadStatus {
    /// 空闲状态
    Idle,
    /// 重载中
    Reloading,
    /// 重载成功
    Success(String),
    /// 重载失败
    Failed(String),
    /// 已回滚
    RolledBack(String),
}
```

#### 方法

##### `is_idle`

```rust
pub fn is_idle(&self) -> bool
```

检查是否为空闲状态。

##### `is_reloading`

```rust
pub fn is_reloading(&self) -> bool
```

检查是否正在重载。

##### `is_success`

```rust
pub fn is_success(&self) -> bool
```

检查是否重载成功。

##### `is_failed`

```rust
pub fn is_failed(&self) -> bool
```

检查是否重载失败。

##### `is_rolled_back`

```rust
pub fn is_rolled_back(&self) -> bool
```

检查是否已回滚。

### ReloadStats

重载统计信息结构体。

```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ReloadStats {
    /// 总重载次数
    pub total_reloads: u64,
    /// 成功重载次数
    pub successful_reloads: u64,
    /// 失败重载次数
    pub failed_reloads: u64,
    /// 回滚次数
    pub rollback_count: u64,
    /// 最后重载时间
    pub last_reload_time: Option<DateTime<Utc>>,
    /// 最后成功重载时间
    pub last_successful_reload: Option<DateTime<Utc>>,
    /// 平均重载时间（毫秒）
    pub average_reload_time_ms: f64,
}
```

#### 方法

##### `success_rate`

```rust
pub fn success_rate(&self) -> f64
```

计算重载成功率（0.0 到 1.0）。

**示例:**
```rust
let stats = service.get_stats();
let rate = stats.success_rate();
println!("成功率: {:.1}%", rate * 100.0);
```

##### `failure_rate`

```rust
pub fn failure_rate(&self) -> f64
```

计算重载失败率（0.0 到 1.0）。

##### `has_recent_activity`

```rust
pub fn has_recent_activity(&self, within: Duration) -> bool
```

检查指定时间内是否有重载活动。

**参数:**
- `within`: 时间范围

**示例:**
```rust
let stats = service.get_stats();
if stats.has_recent_activity(Duration::from_hours(1)) {
    println!("最近1小时内有重载活动");
}
```

## FileWatcher

文件监控器，用于实现热重载功能。

```rust
pub struct FileWatcher {
    // 私有字段
}
```

#### 构造方法

##### `new`

创建新的文件监控器。

```rust
pub fn new(path: PathBuf, debounce_duration: Duration) -> Result<Self, CocoError>
```

**参数:**
- `path`: 要监控的文件路径
- `debounce_duration`: 防抖时间间隔

**返回值:**
- `Ok(FileWatcher)`: 成功创建的监控器
- `Err(CocoError)`: 创建失败

#### 监控方法

##### `start_watching`

开始监控文件变化。

```rust
pub async fn start_watching<F>(&mut self, callback: F) -> Result<(), CocoError>
where
    F: Fn() -> Pin<Box<dyn Future<Output = Result<()>> + Send>> + Send + Sync + 'static,
```

**参数:**
- `callback`: 文件变化时的回调函数

**返回值:**
- `Ok(())`: 监控启动成功
- `Err(CocoError)`: 启动失败

##### `stop_watching`

停止监控文件变化。

```rust
pub async fn stop_watching(&mut self) -> Result<(), CocoError>
```

##### `is_watching`

检查是否正在监控。

```rust
pub fn is_watching(&self) -> bool
```

## 错误类型

### CocoError

配置重载相关的错误类型。

```rust
#[derive(Debug, thiserror::Error)]
pub enum CocoError {
    #[error("配置错误: {0}")]
    ConfigError(String),
    
    #[error("文件系统错误: {0}")]
    FileSystemError(String),
    
    #[error("验证错误: {0}")]
    ValidationError(String),
    
    #[error("IO错误: {0}")]
    IoError(#[from] std::io::Error),
    
    #[error("YAML解析错误: {0}")]
    YamlError(#[from] serde_yaml::Error),
}
```

## 使用示例

### 完整示例

```rust
use coco_server::services::{ConfigReloadService, ReloadStatus};
use std::sync::{Arc, Mutex};
use std::path::PathBuf;
use tokio::time::Duration;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 1. 创建配置管理器
    let config_manager = Arc::new(Mutex::new(ConfigManager::new()?));
    
    // 2. 创建配置重载服务
    let config_path = PathBuf::from("config.yaml");
    let mut service = ConfigReloadService::new(config_manager, &config_path)?;
    
    // 3. 启用热重载
    service.start_hot_reload().await?;
    println!("热重载已启用");
    
    // 4. 手动重载配置
    match service.reload_config().await {
        Ok(_) => println!("手动重载成功"),
        Err(e) => println!("手动重载失败: {}", e),
    }
    
    // 5. 查询状态
    let status = service.get_status();
    println!("当前状态: {:?}", status);
    
    // 6. 查询统计信息
    let stats = service.get_stats();
    println!("重载统计:");
    println!("  总次数: {}", stats.total_reloads);
    println!("  成功率: {:.1}%", stats.success_rate() * 100.0);
    println!("  平均耗时: {:.2}ms", stats.average_reload_time_ms);
    
    // 7. 等待一段时间让热重载工作
    tokio::time::sleep(Duration::from_secs(30)).await;
    
    // 8. 停止热重载
    service.stop_hot_reload().await?;
    println!("热重载已停止");
    
    Ok(())
}
```

### 错误处理示例

```rust
async fn safe_reload(service: &ConfigReloadService) -> Result<(), CocoError> {
    // 检查当前状态
    if service.get_status().is_reloading() {
        return Err(CocoError::ConfigError("配置正在重载中".to_string()));
    }
    
    // 执行重载
    match service.reload_config().await {
        Ok(_) => {
            println!("重载成功");
            
            // 检查最终状态
            let status = service.get_status();
            if status.is_success() {
                println!("配置已生效");
            }
        }
        Err(e) => {
            println!("重载失败: {}", e);
            
            // 检查是否已回滚
            let status = service.get_status();
            if status.is_rolled_back() {
                println!("已自动回滚到安全状态");
            }
            
            return Err(e);
        }
    }
    
    Ok(())
}
```

### 监控集成示例

```rust
async fn monitored_service() -> Result<(), CocoError> {
    let service = ConfigReloadService::new(config_manager, &config_path)?;
    
    // 定期检查服务健康状态
    tokio::spawn(async move {
        let mut interval = tokio::time::interval(Duration::from_secs(60));
        
        loop {
            interval.tick().await;
            
            let stats = service.get_stats();
            let success_rate = stats.success_rate();
            
            // 发送监控指标
            metrics::gauge!("config_reload_success_rate").set(success_rate);
            metrics::gauge!("config_reload_avg_time").set(stats.average_reload_time_ms);
            
            // 健康检查
            if success_rate < 0.9 && stats.total_reloads > 10 {
                tracing::warn!("配置重载成功率过低: {:.1}%", success_rate * 100.0);
            }
        }
    });
    
    Ok(())
}
```

## 注意事项

1. **线程安全**: 所有 API 都是线程安全的，可以在多线程环境中使用
2. **异步操作**: 重载操作是异步的，不会阻塞调用线程
3. **错误恢复**: 重载失败时会自动尝试回滚到上一个有效配置
4. **性能考虑**: 避免频繁调用重载操作，建议使用防抖机制
5. **文件权限**: 确保进程有读写配置文件的权限
6. **配置验证**: 重载前会进行严格的配置验证，确保系统稳定性

## 版本兼容性

- **最低 Rust 版本**: 1.70.0
- **Tokio 版本**: 1.0+
- **支持的平台**: Linux, macOS, Windows

---

更多详细信息请参考：
- [用户指南](config-reload-user-guide.md)
- [技术实现详解](config-reload-technical-details.md)
- [任务完成报告](config-reload-task-report.md)
