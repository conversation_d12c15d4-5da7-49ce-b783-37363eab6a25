# 示例模型提供商配置文件
# 用于演示TASK-015配置管理系统的功能

[metadata]
version = "1.0.0"
description = "示例模型提供商配置文件，展示配置管理系统的功能"
last_updated = "2025-01-15T10:30:00Z"

# OpenAI 提供商配置
[[providers]]
id = "openai"
name = "OpenAI"
api_key = ""  # 用户需要自行配置
api_type = "openai"
base_url = "https://api.openai.com"
icon = "/assets/icons/llm/openai.svg"
enabled = false
builtin = true
description = "OpenAI官方API提供商，支持GPT系列模型"

[[providers.models]]
name = "gpt-4"

[[providers.models]]
name = "gpt-4-turbo"

[[providers.models]]
name = "gpt-3.5-turbo"

# Anthropic 提供商配置
[[providers]]
id = "anthropic"
name = "Anthropic"
api_key = ""  # 用户需要自行配置
api_type = "anthropic"
base_url = "https://api.anthropic.com"
icon = "/assets/icons/llm/anthropic.svg"
enabled = false
builtin = true
description = "Anthropic Claude API提供商，支持Claude系列模型"

[[providers.models]]
name = "claude-3-opus"

[[providers.models]]
name = "claude-3-sonnet"

[[providers.models]]
name = "claude-3-haiku"

# Google 提供商配置
[[providers]]
id = "google"
name = "Google AI"
api_key = ""  # 用户需要自行配置
api_type = "google"
base_url = "https://generativelanguage.googleapis.com"
icon = "/assets/icons/llm/google.svg"
enabled = false
builtin = true
description = "Google AI API提供商，支持Gemini系列模型"

[[providers.models]]
name = "gemini-pro"

[[providers.models]]
name = "gemini-pro-vision"

# Azure OpenAI 提供商配置
[[providers]]
id = "azure_openai"
name = "Azure OpenAI"
api_key = ""  # 用户需要自行配置
api_type = "azure_openai"
base_url = ""  # 用户需要配置Azure端点
icon = "/assets/icons/llm/azure.svg"
enabled = false
builtin = true
description = "Azure OpenAI服务提供商，企业级OpenAI API"

[[providers.models]]
name = "gpt-4"

[[providers.models]]
name = "gpt-35-turbo"

# 本地模型提供商配置示例
[[providers]]
id = "ollama"
name = "Ollama"
api_key = ""  # 本地服务通常不需要API密钥
api_type = "ollama"
base_url = "http://localhost:11434"
icon = "/assets/icons/llm/ollama.svg"
enabled = false
builtin = true
description = "Ollama本地模型服务提供商，支持多种开源模型"

[[providers.models]]
name = "llama2"

[[providers.models]]
name = "codellama"

[[providers.models]]
name = "mistral"
