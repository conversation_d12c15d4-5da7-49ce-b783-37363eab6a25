use std::{collections::HashMap, fs, path::Path};

use serde::{Deserialize, Serialize};
use tracing::{debug, error, info, warn};

use crate::error::{error::CocoError, result::Result};

/// TOML配置文件解析器
/// 负责解析model_provider.toml等TOML格式的配置文件
#[derive(Debug, Clone)]
pub struct TomlParser {
    /// 配置文件路径
    config_path: String,
    /// 解析后的配置内容
    parsed_config: Option<BuiltinProviderConfig>,
}

/// 内置提供商配置的元数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConfigMetadata {
    /// 配置版本
    pub version: String,
    /// 配置描述
    pub description: String,
    /// 最后更新时间
    pub last_updated: String,
}

/// 内置提供商配置结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BuiltinProviderConfig {
    /// 配置元数据
    pub metadata: ConfigMetadata,
    /// 提供商列表
    pub providers: Vec<ProviderConfig>,
}

/// 提供商配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProviderConfig {
    /// 提供商ID
    pub id: String,
    /// 提供商名称
    pub name: String,
    /// API密钥（通常为空，由用户配置）
    pub api_key: String,
    /// API类型
    pub api_type: String,
    /// 基础URL
    pub base_url: String,
    /// 图标
    pub icon: String,
    /// 是否启用
    pub enabled: bool,
    /// 是否为内置提供商
    pub builtin: bool,
    /// 描述
    pub description: String,
    /// 支持的模型列表
    pub models: Vec<ModelConfig>,
}

/// 模型配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModelConfig {
    /// 模型名称
    pub name: String,
}

impl TomlParser {
    /// 创建新的TOML解析器实例
    pub fn new<P: AsRef<Path>>(config_path: P) -> Self {
        let path_str = config_path.as_ref().to_string_lossy().to_string();
        info!("创建TOML解析器，配置文件路径: {}", path_str);

        Self {
            config_path: path_str,
            parsed_config: None,
        }
    }

    /// 解析TOML配置文件
    pub fn parse(&mut self) -> Result<&BuiltinProviderConfig> {
        info!("开始解析TOML配置文件: {}", self.config_path);

        // 检查文件是否存在
        if !Path::new(&self.config_path).exists() {
            let error_msg = format!("配置文件不存在: {}", self.config_path);
            error!("{}", error_msg);
            return Err(CocoError::ConfigError(error_msg));
        }

        // 读取文件内容
        let content = fs::read_to_string(&self.config_path).map_err(|e| {
            let error_msg = format!("读取配置文件失败 '{}': {}", self.config_path, e);
            error!("{}", error_msg);
            CocoError::ConfigError(error_msg)
        })?;

        // 解析TOML内容
        let config: BuiltinProviderConfig = toml::from_str(&content).map_err(|e| {
            let error_msg = format!("解析TOML配置文件失败 '{}': {}", self.config_path, e);
            error!("{}", error_msg);
            CocoError::ConfigError(error_msg)
        })?;

        // 验证配置
        self.validate_config(&config)?;

        // 存储解析结果
        self.parsed_config = Some(config);

        info!(
            "TOML配置文件解析成功，共加载 {} 个提供商",
            self.parsed_config.as_ref().unwrap().providers.len()
        );

        Ok(self.parsed_config.as_ref().unwrap())
    }

    /// 获取解析后的配置
    pub fn get_config(&self) -> Option<&BuiltinProviderConfig> {
        self.parsed_config.as_ref()
    }

    /// 重新加载配置文件
    pub fn reload(&mut self) -> Result<&BuiltinProviderConfig> {
        info!("重新加载TOML配置文件: {}", self.config_path);
        self.parsed_config = None;
        self.parse()
    }

    /// 验证配置的有效性
    fn validate_config(&self, config: &BuiltinProviderConfig) -> Result<()> {
        debug!("开始验证TOML配置");

        // 验证元数据
        if config.metadata.version.is_empty() {
            return Err(CocoError::ConfigError("配置版本不能为空".to_string()));
        }

        // 验证提供商配置
        let mut provider_ids = HashMap::new();
        for (index, provider) in config.providers.iter().enumerate() {
            // 验证必填字段
            if provider.id.is_empty() {
                return Err(CocoError::ConfigError(format!(
                    "提供商 {} 的ID不能为空",
                    index
                )));
            }
            if provider.name.is_empty() {
                return Err(CocoError::ConfigError(format!(
                    "提供商 {} 的名称不能为空",
                    index
                )));
            }
            if provider.api_type.is_empty() {
                return Err(CocoError::ConfigError(format!(
                    "提供商 {} 的API类型不能为空",
                    index
                )));
            }

            // 检查ID唯一性
            if provider_ids.contains_key(&provider.id) {
                return Err(CocoError::ConfigError(format!(
                    "提供商ID重复: {}",
                    provider.id
                )));
            }
            provider_ids.insert(&provider.id, index);

            // 验证URL格式（如果不为空）
            if !provider.base_url.is_empty() {
                if let Err(_) = url::Url::parse(&provider.base_url) {
                    warn!(
                        "提供商 {} 的base_url格式可能不正确: {}",
                        provider.id, provider.base_url
                    );
                }
            }

            // 验证模型配置
            for (model_index, model) in provider.models.iter().enumerate() {
                if model.name.is_empty() {
                    return Err(CocoError::ConfigError(format!(
                        "提供商 {} 的模型 {} 名称不能为空",
                        provider.id, model_index
                    )));
                }
            }
        }

        info!(
            "TOML配置验证通过，共验证 {} 个提供商",
            config.providers.len()
        );
        Ok(())
    }

    /// 获取配置文件路径
    pub fn get_config_path(&self) -> &str {
        &self.config_path
    }

    /// 获取配置版本
    pub fn get_version(&self) -> Option<String> {
        self.parsed_config
            .as_ref()
            .map(|config| config.metadata.version.clone())
    }

    /// 获取提供商数量
    pub fn get_provider_count(&self) -> usize {
        self.parsed_config
            .as_ref()
            .map(|config| config.providers.len())
            .unwrap_or(0)
    }

    /// 根据ID查找提供商配置
    pub fn find_provider_by_id(&self, id: &str) -> Option<&ProviderConfig> {
        self.parsed_config
            .as_ref()
            .and_then(|config| config.providers.iter().find(|provider| provider.id == id))
    }

    /// 获取所有内置提供商ID列表
    pub fn get_builtin_provider_ids(&self) -> Vec<String> {
        self.parsed_config
            .as_ref()
            .map(|config| {
                config
                    .providers
                    .iter()
                    .filter(|provider| provider.builtin)
                    .map(|provider| provider.id.clone())
                    .collect()
            })
            .unwrap_or_default()
    }

    /// 检查配置文件是否已修改
    pub fn is_file_modified(&self, last_modified: std::time::SystemTime) -> Result<bool> {
        let metadata = fs::metadata(&self.config_path)
            .map_err(|e| CocoError::ConfigError(format!("获取配置文件元数据失败: {}", e)))?;

        let current_modified = metadata
            .modified()
            .map_err(|e| CocoError::ConfigError(format!("获取配置文件修改时间失败: {}", e)))?;

        Ok(current_modified > last_modified)
    }
}

#[cfg(test)]
mod tests {
    use std::fs;

    use tempfile::NamedTempFile;

    use super::*;

    #[test]
    fn test_parse_valid_toml() {
        let toml_content = r#"
[metadata]
version = "1.0.0"
description = "测试配置"
last_updated = "2025-01-15T10:30:00Z"

[[providers]]
id = "test_provider"
name = "测试提供商"
api_key = ""
api_type = "openai"
base_url = "https://api.test.com"
icon = "test_icon"
enabled = false
builtin = true
description = "测试描述"

[[providers.models]]
name = "test-model"
"#;

        let temp_file = NamedTempFile::new().unwrap();
        fs::write(temp_file.path(), toml_content).unwrap();

        let mut parser = TomlParser::new(temp_file.path());
        let config = parser.parse().unwrap();

        assert_eq!(config.metadata.version, "1.0.0");
        assert_eq!(config.providers.len(), 1);
        assert_eq!(config.providers[0].id, "test_provider");
        assert_eq!(config.providers[0].models.len(), 1);
    }

    #[test]
    fn test_parse_invalid_toml() {
        let invalid_toml = "invalid toml content";

        let temp_file = NamedTempFile::new().unwrap();
        fs::write(temp_file.path(), invalid_toml).unwrap();

        let mut parser = TomlParser::new(temp_file.path());
        assert!(parser.parse().is_err());
    }

    #[test]
    fn test_validate_duplicate_provider_ids() {
        let toml_content = r#"
[metadata]
version = "1.0.0"
description = "测试配置"
last_updated = "2025-01-15T10:30:00Z"

[[providers]]
id = "duplicate_id"
name = "提供商1"
api_key = ""
api_type = "openai"
base_url = "https://api.test1.com"
icon = "icon1"
enabled = false
builtin = true
description = "描述1"

[[providers.models]]
name = "model1"

[[providers]]
id = "duplicate_id"
name = "提供商2"
api_key = ""
api_type = "openai"
base_url = "https://api.test2.com"
icon = "icon2"
enabled = false
builtin = true
description = "描述2"

[[providers.models]]
name = "model2"
"#;

        let temp_file = NamedTempFile::new().unwrap();
        fs::write(temp_file.path(), toml_content).unwrap();

        let mut parser = TomlParser::new(temp_file.path());
        assert!(parser.parse().is_err());
    }

    #[test]
    fn test_find_provider_by_id() {
        let toml_content = r#"
[metadata]
version = "1.0.0"
description = "测试配置"
last_updated = "2025-01-15T10:30:00Z"

[[providers]]
id = "openai"
name = "OpenAI"
api_key = ""
api_type = "openai"
base_url = "https://api.openai.com"
icon = "openai_icon"
enabled = false
builtin = true
description = "OpenAI提供商"

[[providers.models]]
name = "gpt-4"
"#;

        let temp_file = NamedTempFile::new().unwrap();
        fs::write(temp_file.path(), toml_content).unwrap();

        let mut parser = TomlParser::new(temp_file.path());
        parser.parse().unwrap();

        let provider = parser.find_provider_by_id("openai");
        assert!(provider.is_some());
        assert_eq!(provider.unwrap().name, "OpenAI");

        let non_existent = parser.find_provider_by_id("non_existent");
        assert!(non_existent.is_none());
    }

    #[test]
    fn test_get_builtin_provider_ids() {
        let toml_content = r#"
[metadata]
version = "1.0.0"
description = "测试配置"
last_updated = "2025-01-15T10:30:00Z"

[[providers]]
id = "builtin1"
name = "内置提供商1"
api_key = ""
api_type = "openai"
base_url = "https://api.test1.com"
icon = "icon1"
enabled = false
builtin = true
description = "内置提供商1"

[[providers.models]]
name = "model1"

[[providers]]
id = "custom1"
name = "自定义提供商1"
api_key = ""
api_type = "openai"
base_url = "https://api.test2.com"
icon = "icon2"
enabled = false
builtin = false
description = "自定义提供商1"

[[providers.models]]
name = "model2"
"#;

        let temp_file = NamedTempFile::new().unwrap();
        fs::write(temp_file.path(), toml_content).unwrap();

        let mut parser = TomlParser::new(temp_file.path());
        parser.parse().unwrap();

        let builtin_ids = parser.get_builtin_provider_ids();
        assert_eq!(builtin_ids.len(), 1);
        assert!(builtin_ids.contains(&"builtin1".to_string()));
        assert!(!builtin_ids.contains(&"custom1".to_string()));
    }
}
