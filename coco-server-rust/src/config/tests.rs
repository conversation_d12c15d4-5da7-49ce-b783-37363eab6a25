#[cfg(test)]
mod config_manager_tests {
    use std::fs;

    use tempfile::NamedTempFile;

    use super::super::{
        builtin_provider_config::BuiltinProviderConfigManager, config_manager::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
        toml_parser::<PERSON><PERSON><PERSON><PERSON><PERSON>,
    };

    fn create_test_toml_config() -> String {
        r#"
[metadata]
version = "1.0.0"
description = "测试配置文件"
last_updated = "2025-01-15T10:30:00Z"

[[providers]]
id = "openai"
name = "OpenAI"
api_key = ""
api_type = "openai"
base_url = "https://api.openai.com"
icon = "/assets/icons/llm/openai.svg"
enabled = false
builtin = true
description = "OpenAI官方API提供商"

[[providers.models]]
name = "gpt-4"

[[providers.models]]
name = "gpt-3.5-turbo"

[[providers]]
id = "anthropic"
name = "Anthropic"
api_key = ""
api_type = "anthropic"
base_url = "https://api.anthropic.com"
icon = "/assets/icons/llm/anthropic.svg"
enabled = false
builtin = true
description = "Anthropic Claude API提供商"

[[providers.models]]
name = "claude-3-opus"

[[providers.models]]
name = "claude-3-sonnet"
"#
        .to_string()
    }

    #[test]
    fn test_toml_parser_basic_functionality() {
        let toml_content = create_test_toml_config();
        let temp_file = NamedTempFile::new().unwrap();
        fs::write(temp_file.path(), toml_content).unwrap();

        let mut parser = TomlParser::new(temp_file.path());
        let config = parser.parse().unwrap();

        // 验证基本信息
        assert_eq!(config.metadata.version, "1.0.0");
        assert_eq!(config.providers.len(), 2);

        // 验证第一个提供商
        let openai = &config.providers[0];
        assert_eq!(openai.id, "openai");
        assert_eq!(openai.name, "OpenAI");
        assert_eq!(openai.api_type, "openai");
        assert_eq!(openai.builtin, true);
        assert_eq!(openai.models.len(), 2);

        // 验证第二个提供商
        let anthropic = &config.providers[1];
        assert_eq!(anthropic.id, "anthropic");
        assert_eq!(anthropic.name, "Anthropic");
        assert_eq!(anthropic.api_type, "anthropic");
        assert_eq!(anthropic.builtin, true);
        assert_eq!(anthropic.models.len(), 2);
    }

    #[test]
    fn test_toml_parser_validation() {
        // 测试重复ID的情况
        let invalid_toml = r#"
[metadata]
version = "1.0.0"
description = "测试配置"
last_updated = "2025-01-15T10:30:00Z"

[[providers]]
id = "duplicate"
name = "提供商1"
api_key = ""
api_type = "openai"
base_url = "https://api.test1.com"
icon = "icon1"
enabled = false
builtin = true
description = "描述1"

[[providers.models]]
name = "model1"

[[providers]]
id = "duplicate"
name = "提供商2"
api_key = ""
api_type = "openai"
base_url = "https://api.test2.com"
icon = "icon2"
enabled = false
builtin = true
description = "描述2"

[[providers.models]]
name = "model2"
"#;

        let temp_file = NamedTempFile::new().unwrap();
        fs::write(temp_file.path(), invalid_toml).unwrap();

        let mut parser = TomlParser::new(temp_file.path());
        assert!(parser.parse().is_err());
    }

    #[test]
    fn test_builtin_provider_config_manager() {
        let toml_content = create_test_toml_config();
        let temp_file = NamedTempFile::new().unwrap();
        fs::write(temp_file.path(), toml_content).unwrap();

        let mut manager = BuiltinProviderConfigManager::new(temp_file.path());

        // 测试加载配置
        let config = manager.load_config().unwrap();
        assert_eq!(config.providers.len(), 2);
        assert_eq!(manager.get_config_version(), Some(&"1.0.0".to_string()));

        // 测试转换为ModelProvider
        let providers = manager.convert_to_model_providers().unwrap();
        assert_eq!(providers.len(), 2);

        // 验证转换结果
        let openai_provider = &providers[0];
        assert_eq!(openai_provider.name, "OpenAI");
        assert_eq!(openai_provider.api_type, "openai");
        assert_eq!(openai_provider.builtin, true);
        assert!(!openai_provider.models.is_empty());

        // 测试配置统计
        let stats = manager.get_config_stats().unwrap();
        assert_eq!(stats.total_providers, 2);
        assert_eq!(stats.builtin_providers, 2);
        assert_eq!(stats.total_models, 4);
    }

    #[test]
    fn test_config_manager_integration() {
        let toml_content = create_test_toml_config();
        let temp_file = NamedTempFile::new().unwrap();
        fs::write(temp_file.path(), toml_content).unwrap();

        let mut config_manager = ConfigManager::new().unwrap();

        // 初始化内置提供商管理器
        config_manager
            .init_builtin_provider_manager(temp_file.path())
            .unwrap();

        // 获取管理器并加载配置
        let builtin_manager = config_manager.get_builtin_provider_manager().unwrap();
        {
            let mut mgr = builtin_manager.lock().unwrap();
            mgr.load_config().unwrap();
        }

        // 验证配置版本管理
        config_manager.set_config_version("1.0.0".to_string());
        assert_eq!(config_manager.get_config_version(), "1.0.0");

        // 测试配置验证
        assert!(config_manager.validate_toml_config().is_ok());
    }

    #[test]
    fn test_toml_parser_file_operations() {
        let toml_content = create_test_toml_config();
        let temp_file = NamedTempFile::new().unwrap();
        fs::write(temp_file.path(), toml_content).unwrap();

        let mut parser = TomlParser::new(temp_file.path());

        // 测试初始解析
        parser.parse().unwrap();
        assert_eq!(parser.get_provider_count(), 2);
        assert_eq!(parser.get_version(), Some("1.0.0".to_string()));

        // 测试查找提供商
        let openai = parser.find_provider_by_id("openai");
        assert!(openai.is_some());
        assert_eq!(openai.unwrap().name, "OpenAI");

        let non_existent = parser.find_provider_by_id("non_existent");
        assert!(non_existent.is_none());

        // 测试获取内置提供商ID
        let builtin_ids = parser.get_builtin_provider_ids();
        assert_eq!(builtin_ids.len(), 2);
        assert!(builtin_ids.contains(&"openai".to_string()));
        assert!(builtin_ids.contains(&"anthropic".to_string()));

        // 测试重新加载
        let reloaded_config = parser.reload().unwrap();
        assert_eq!(reloaded_config.providers.len(), 2);
    }

    #[test]
    fn test_config_error_handling() {
        // 测试不存在的文件
        let mut parser = TomlParser::new("/non/existent/path.toml");
        assert!(parser.parse().is_err());

        // 测试无效的TOML格式
        let invalid_toml = "invalid toml content [[[";
        let temp_file = NamedTempFile::new().unwrap();
        fs::write(temp_file.path(), invalid_toml).unwrap();

        let mut parser = TomlParser::new(temp_file.path());
        assert!(parser.parse().is_err());

        // 测试缺少必填字段的配置
        let incomplete_toml = r#"
[metadata]
version = ""
description = "测试"
last_updated = "2025-01-15T10:30:00Z"

[[providers]]
id = ""
name = ""
api_key = ""
api_type = ""
base_url = ""
icon = ""
enabled = false
builtin = true
description = ""
"#;

        let temp_file = NamedTempFile::new().unwrap();
        fs::write(temp_file.path(), incomplete_toml).unwrap();

        let mut parser = TomlParser::new(temp_file.path());
        assert!(parser.parse().is_err());
    }

    #[test]
    fn test_hot_reload_functionality() {
        let toml_content = create_test_toml_config();
        let temp_file = NamedTempFile::new().unwrap();
        fs::write(temp_file.path(), toml_content).unwrap();

        let mut config_manager = ConfigManager::new().unwrap();
        config_manager
            .init_builtin_provider_manager(temp_file.path())
            .unwrap();

        // 测试启用热重载
        assert!(!config_manager.is_hot_reload_enabled());
        config_manager.enable_hot_reload(temp_file.path()).unwrap();
        assert!(config_manager.is_hot_reload_enabled());

        // 测试禁用热重载
        config_manager.disable_hot_reload();
        assert!(!config_manager.is_hot_reload_enabled());

        // 测试手动重新加载
        let builtin_manager = config_manager.get_builtin_provider_manager().unwrap();
        {
            let mut mgr = builtin_manager.lock().unwrap();
            mgr.load_config().unwrap();
        }
        assert!(config_manager.manual_reload().is_ok());
    }
}
