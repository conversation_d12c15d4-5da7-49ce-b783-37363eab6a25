use std::sync::Arc;

use crate::{
    auth::{jwt_cache::JwtCache, token_blacklist::TokenBlacklist},
    config::config_manager::ConfigManager,
    database::SurrealDBClient,
    repositories::token_repository::TokenRepository,
    services::{config_reload_service::ConfigReloadService, token_service::TokenService},
};

/// 应用状态结构
#[derive(Clone)]
pub struct AppState {
    pub config_manager: Arc<ConfigManager>,
    pub db_client: Option<Arc<SurrealDBClient>>, // 可选，用于向后兼容
    pub token_repository: Arc<TokenRepository>,
    pub token_service: Arc<TokenService>,
    pub token_blacklist: Arc<TokenBlacklist>,
    pub jwt_cache: Arc<JwtCache>,
    pub config_reload_service: Option<Arc<ConfigReloadService>>, // 配置重载服务
}

impl AppState {
    /// 创建新的应用状态（向后兼容）
    pub fn new(
        config_manager: Arc<ConfigManager>,
        db_client: Arc<SurrealDBClient>,
        token_repository: Arc<TokenRepository>,
        token_service: Arc<TokenService>,
        token_blacklist: Arc<TokenBlacklist>,
        jwt_cache: Arc<JwtCache>,
    ) -> Self {
        Self {
            config_manager,
            db_client: Some(db_client),
            token_repository,
            token_service,
            token_blacklist,
            jwt_cache,
            config_reload_service: None,
        }
    }

    /// 创建使用全局DB连接的应用状态
    pub fn new_with_global_db(
        config_manager: Arc<ConfigManager>,
        token_repository: Arc<TokenRepository>,
        token_service: Arc<TokenService>,
        token_blacklist: Arc<TokenBlacklist>,
        jwt_cache: Arc<JwtCache>,
    ) -> Self {
        Self {
            config_manager,
            db_client: None,
            token_repository,
            token_service,
            token_blacklist,
            jwt_cache,
            config_reload_service: None,
        }
    }

    /// 设置配置重载服务
    pub fn set_config_reload_service(&mut self, service: Arc<ConfigReloadService>) {
        self.config_reload_service = Some(service);
    }

    /// 获取配置重载服务
    pub fn get_config_reload_service(&self) -> Option<Arc<ConfigReloadService>> {
        self.config_reload_service.clone()
    }

    /// 获取配置管理器
    pub fn get_config_manager(&self) -> Arc<ConfigManager> {
        self.config_manager.clone()
    }
}
