use std::sync::Arc;

use surrealdb::{engine::remote::ws::Client, Surreal};
use tracing::{error, info, warn};

use crate::error::{error::CocoError, result::Result};

/// 数据库设置服务
///
/// 负责数据库连接检查、表结构创建和数据库初始化
#[derive(Clone)]
pub struct DatabaseSetupService {
    /// 数据库连接
    db: Arc<Surreal<Client>>,
}

impl DatabaseSetupService {
    /// 创建新的数据库设置服务
    pub fn new(db: Arc<Surreal<Client>>) -> Self {
        Self { db }
    }

    /// 检查数据库连接
    pub async fn check_database_connection(&self) -> Result<()> {
        info!("检查数据库连接...");

        // 尝试执行一个简单的查询来验证连接
        match self.db.query("SELECT 1 as test").await {
            Ok(_) => {
                info!("数据库连接检查成功");
                Ok(())
            }
            Err(e) => {
                error!("数据库连接检查失败: {}", e);
                Err(CocoError::Database(format!("数据库连接失败: {}", e)))
            }
        }
    }

    /// 确保数据库表结构存在
    pub async fn ensure_database_schema(&self) -> Result<()> {
        info!("检查并创建数据库表结构...");

        // 创建模型提供商表
        self.create_model_provider_table().await?;

        // 创建数据源表
        self.create_datasource_table().await?;

        // 创建访问令牌表
        self.create_access_token_table().await?;

        // 创建连接器表
        self.create_connector_table().await?;

        // 创建MCP服务器表
        self.create_mcp_server_table().await?;

        info!("数据库表结构检查完成");
        Ok(())
    }

    /// 创建模型提供商表
    async fn create_model_provider_table(&self) -> Result<()> {
        info!("创建模型提供商表...");

        let query = r#"
            DEFINE TABLE model_provider SCHEMAFULL;

            DEFINE FIELD id ON TABLE model_provider TYPE string;
            DEFINE FIELD created ON TABLE model_provider TYPE datetime DEFAULT time::now();
            DEFINE FIELD updated ON TABLE model_provider TYPE datetime DEFAULT time::now();
            DEFINE FIELD name ON TABLE model_provider TYPE string;
            DEFINE FIELD api_key ON TABLE model_provider TYPE string;
            DEFINE FIELD api_type ON TABLE model_provider TYPE string;
            DEFINE FIELD base_url ON TABLE model_provider TYPE string;
            DEFINE FIELD icon ON TABLE model_provider TYPE string DEFAULT "";
            DEFINE FIELD models ON TABLE model_provider TYPE array DEFAULT [];
            DEFINE FIELD enabled ON TABLE model_provider TYPE bool DEFAULT true;
            DEFINE FIELD builtin ON TABLE model_provider TYPE bool DEFAULT false;
            DEFINE FIELD description ON TABLE model_provider TYPE string DEFAULT "";

            DEFINE INDEX name_idx ON TABLE model_provider COLUMNS name UNIQUE;
            DEFINE INDEX enabled_idx ON TABLE model_provider COLUMNS enabled;
            DEFINE INDEX builtin_idx ON TABLE model_provider COLUMNS builtin;
            DEFINE INDEX api_type_idx ON TABLE model_provider COLUMNS api_type;
        "#;

        match self.db.query(query).await {
            Ok(_) => {
                info!("模型提供商表创建成功");
                Ok(())
            }
            Err(e) => {
                warn!("模型提供商表创建失败或已存在: {}", e);
                // 表可能已经存在，这不是错误
                Ok(())
            }
        }
    }

    /// 创建数据源表
    async fn create_datasource_table(&self) -> Result<()> {
        info!("创建数据源表...");

        let query = r#"
            DEFINE TABLE datasource SCHEMAFULL;

            DEFINE FIELD id ON TABLE datasource TYPE string;
            DEFINE FIELD created ON TABLE datasource TYPE datetime DEFAULT time::now();
            DEFINE FIELD updated ON TABLE datasource TYPE datetime DEFAULT time::now();
            DEFINE FIELD name ON TABLE datasource TYPE string;
            DEFINE FIELD type ON TABLE datasource TYPE string;
            DEFINE FIELD config ON TABLE datasource TYPE object DEFAULT {};
            DEFINE FIELD enabled ON TABLE datasource TYPE bool DEFAULT true;
            DEFINE FIELD description ON TABLE datasource TYPE string DEFAULT "";

            DEFINE INDEX name_idx ON TABLE datasource COLUMNS name UNIQUE;
            DEFINE INDEX type_idx ON TABLE datasource COLUMNS type;
            DEFINE INDEX enabled_idx ON TABLE datasource COLUMNS enabled;
        "#;

        match self.db.query(query).await {
            Ok(_) => {
                info!("数据源表创建成功");
                Ok(())
            }
            Err(e) => {
                warn!("数据源表创建失败或已存在: {}", e);
                Ok(())
            }
        }
    }

    /// 创建访问令牌表
    async fn create_access_token_table(&self) -> Result<()> {
        info!("创建访问令牌表...");

        let query = r#"
            DEFINE TABLE access_token SCHEMAFULL;

            DEFINE FIELD id ON TABLE access_token TYPE string;
            DEFINE FIELD created ON TABLE access_token TYPE datetime DEFAULT time::now();
            DEFINE FIELD updated ON TABLE access_token TYPE datetime DEFAULT time::now();
            DEFINE FIELD token ON TABLE access_token TYPE string;
            DEFINE FIELD user_id ON TABLE access_token TYPE string;
            DEFINE FIELD expires_at ON TABLE access_token TYPE datetime;
            DEFINE FIELD revoked ON TABLE access_token TYPE bool DEFAULT false;
            DEFINE FIELD description ON TABLE access_token TYPE string DEFAULT "";

            DEFINE INDEX token_idx ON TABLE access_token COLUMNS token UNIQUE;
            DEFINE INDEX user_id_idx ON TABLE access_token COLUMNS user_id;
            DEFINE INDEX expires_at_idx ON TABLE access_token COLUMNS expires_at;
            DEFINE INDEX revoked_idx ON TABLE access_token COLUMNS revoked;
        "#;

        match self.db.query(query).await {
            Ok(_) => {
                info!("访问令牌表创建成功");
                Ok(())
            }
            Err(e) => {
                warn!("访问令牌表创建失败或已存在: {}", e);
                Ok(())
            }
        }
    }

    /// 创建连接器表
    async fn create_connector_table(&self) -> Result<()> {
        info!("创建连接器表...");

        let query = r#"
            DEFINE TABLE connector SCHEMAFULL;

            DEFINE FIELD id ON TABLE connector TYPE string;
            DEFINE FIELD created ON TABLE connector TYPE datetime DEFAULT time::now();
            DEFINE FIELD updated ON TABLE connector TYPE datetime DEFAULT time::now();
            DEFINE FIELD name ON TABLE connector TYPE string;
            DEFINE FIELD type ON TABLE connector TYPE string;
            DEFINE FIELD config ON TABLE connector TYPE object DEFAULT {};
            DEFINE FIELD enabled ON TABLE connector TYPE bool DEFAULT true;
            DEFINE FIELD description ON TABLE connector TYPE string DEFAULT "";

            DEFINE INDEX name_idx ON TABLE connector COLUMNS name UNIQUE;
            DEFINE INDEX type_idx ON TABLE connector COLUMNS type;
            DEFINE INDEX enabled_idx ON TABLE connector COLUMNS enabled;
        "#;

        match self.db.query(query).await {
            Ok(_) => {
                info!("连接器表创建成功");
                Ok(())
            }
            Err(e) => {
                warn!("连接器表创建失败或已存在: {}", e);
                Ok(())
            }
        }
    }

    /// 创建MCP服务器表
    async fn create_mcp_server_table(&self) -> Result<()> {
        info!("创建MCP服务器表...");

        let query = r#"
            DEFINE TABLE mcp_server SCHEMAFULL;

            DEFINE FIELD id ON TABLE mcp_server TYPE string;
            DEFINE FIELD created ON TABLE mcp_server TYPE datetime DEFAULT time::now();
            DEFINE FIELD updated ON TABLE mcp_server TYPE datetime DEFAULT time::now();
            DEFINE FIELD name ON TABLE mcp_server TYPE string;
            DEFINE FIELD command ON TABLE mcp_server TYPE string;
            DEFINE FIELD args ON TABLE mcp_server TYPE array DEFAULT [];
            DEFINE FIELD env ON TABLE mcp_server TYPE object DEFAULT {};
            DEFINE FIELD enabled ON TABLE mcp_server TYPE bool DEFAULT true;
            DEFINE FIELD description ON TABLE mcp_server TYPE string DEFAULT "";

            DEFINE INDEX name_idx ON TABLE mcp_server COLUMNS name UNIQUE;
            DEFINE INDEX enabled_idx ON TABLE mcp_server COLUMNS enabled;
        "#;

        match self.db.query(query).await {
            Ok(_) => {
                info!("MCP服务器表创建成功");
                Ok(())
            }
            Err(e) => {
                warn!("MCP服务器表创建失败或已存在: {}", e);
                Ok(())
            }
        }
    }

    /// 验证表结构
    pub async fn validate_schema(&self) -> Result<()> {
        info!("验证数据库表结构...");

        // 检查所有必需的表是否存在
        let tables = vec![
            "model_provider",
            "datasource",
            "access_token",
            "connector",
            "mcp_server",
        ];

        for table in tables {
            match self.check_table_exists(table).await {
                Ok(exists) => {
                    if exists {
                        info!("表 {} 存在", table);
                    } else {
                        error!("表 {} 不存在", table);
                        return Err(CocoError::Database(format!("必需的表 {} 不存在", table)));
                    }
                }
                Err(e) => {
                    error!("检查表 {} 时出错: {}", table, e);
                    return Err(e);
                }
            }
        }

        info!("数据库表结构验证完成");
        Ok(())
    }

    /// 检查表是否存在
    async fn check_table_exists(&self, table_name: &str) -> Result<bool> {
        let query = format!("INFO FOR TABLE {}", table_name);

        match self.db.query(&query).await {
            Ok(_) => Ok(true),
            Err(_) => Ok(false), // 表不存在时会返回错误
        }
    }

    /// 清理数据库（仅用于测试）
    #[cfg(test)]
    pub async fn cleanup_database(&self) -> Result<()> {
        warn!("清理数据库（仅测试环境）...");

        let tables = vec![
            "model_provider",
            "datasource",
            "access_token",
            "connector",
            "mcp_server",
        ];

        for table in tables {
            let query = format!("REMOVE TABLE {}", table);
            if let Err(e) = self.db.query(&query).await {
                warn!("删除表 {} 失败: {}", table, e);
            }
        }

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::database::{client::SurrealDBClient, config::DatabaseConfig};

    async fn create_test_db() -> Arc<Surreal<Client>> {
        let config = DatabaseConfig::default();
        let client = SurrealDBClient::new(config).await.unwrap();
        Arc::new(client.db().clone())
    }

    #[tokio::test]
    async fn test_database_connection() {
        let db = create_test_db().await;
        let service = DatabaseSetupService::new(db);

        assert!(service.check_database_connection().await.is_ok());
    }

    #[tokio::test]
    async fn test_schema_creation() {
        let db = create_test_db().await;
        let service = DatabaseSetupService::new(db);

        // 清理数据库
        service.cleanup_database().await.unwrap();

        // 创建表结构
        assert!(service.ensure_database_schema().await.is_ok());

        // 验证表结构
        assert!(service.validate_schema().await.is_ok());
    }

    #[tokio::test]
    async fn test_table_exists() {
        let db = create_test_db().await;
        let service = DatabaseSetupService::new(db);

        // 确保表结构存在
        service.ensure_database_schema().await.unwrap();

        // 检查表是否存在
        assert!(service.check_table_exists("model_provider").await.unwrap());
        assert!(!service
            .check_table_exists("non_existent_table")
            .await
            .unwrap());
    }
}
