use std::sync::Arc;

use surrealdb::{engine::remote::ws::Client, Surreal};
use tracing::{error, info};

use crate::{
    config::config_manager::ConfigManager, error::result::Result,
    repositories::model_provider_repo::ModelProviderRepository,
};

/// 系统验证服务
///
/// 负责验证系统状态、配置完整性和数据一致性
#[derive(Clone)]
pub struct SystemValidator {
    /// 数据库连接
    db: Arc<Surreal<Client>>,
    /// 配置管理器
    config_manager: Arc<ConfigManager>,
    /// 模型提供商仓储
    model_provider_repo: Arc<dyn ModelProviderRepository>,
}

/// 系统验证结果
#[derive(Debug, Clone)]
pub struct SystemValidationResult {
    /// 验证是否通过
    pub passed: bool,
    /// 验证项目结果
    pub checks: Vec<ValidationCheck>,
    /// 总体评分 (0-100)
    pub score: u8,
    /// 错误信息
    pub errors: Vec<String>,
    /// 警告信息
    pub warnings: Vec<String>,
}

/// 单个验证检查结果
#[derive(Debug, Clone)]
pub struct ValidationCheck {
    /// 检查名称
    pub name: String,
    /// 检查是否通过
    pub passed: bool,
    /// 检查描述
    pub description: String,
    /// 错误信息（如果有）
    pub error: Option<String>,
    /// 权重 (1-10)
    pub weight: u8,
}

impl SystemValidator {
    /// 创建新的系统验证器
    pub fn new(
        db: Arc<Surreal<Client>>,
        config_manager: Arc<ConfigManager>,
        model_provider_repo: Arc<dyn ModelProviderRepository>,
    ) -> Self {
        Self {
            db,
            config_manager,
            model_provider_repo,
        }
    }

    /// 执行完整的系统验证
    pub async fn validate_system(&self) -> Result<SystemValidationResult> {
        info!("开始系统验证...");

        let mut checks = Vec::new();
        let mut errors = Vec::new();
        let warnings = Vec::new();

        // 1. 数据库连接验证
        checks.push(self.validate_database_connection().await);

        // 2. 配置文件验证
        checks.push(self.validate_configuration().await);

        // 3. 数据库表结构验证
        checks.push(self.validate_database_schema().await);

        // 4. 内置提供商验证
        checks.push(self.validate_builtin_providers().await);

        // 5. 数据一致性验证
        checks.push(self.validate_data_consistency().await);

        // 6. 系统资源验证
        checks.push(self.validate_system_resources().await);

        // 收集错误和警告
        for check in &checks {
            if !check.passed {
                if let Some(error) = &check.error {
                    errors.push(format!("{}: {}", check.name, error));
                }
            }
        }

        // 计算总体评分
        let score = self.calculate_score(&checks);
        let passed = score >= 80; // 80分以上认为通过

        let result = SystemValidationResult {
            passed,
            checks,
            score,
            errors,
            warnings,
        };

        if result.passed {
            info!("系统验证通过，评分: {}/100", result.score);
        } else {
            error!("系统验证失败，评分: {}/100", result.score);
            for error in &result.errors {
                error!("验证错误: {}", error);
            }
        }

        Ok(result)
    }

    /// 验证数据库连接
    async fn validate_database_connection(&self) -> ValidationCheck {
        let name = "数据库连接".to_string();
        let description = "验证SurrealDB数据库连接是否正常".to_string();

        match self.db.query("SELECT 1 as test").await {
            Ok(_) => ValidationCheck {
                name,
                passed: true,
                description,
                error: None,
                weight: 10,
            },
            Err(e) => ValidationCheck {
                name,
                passed: false,
                description,
                error: Some(format!("数据库连接失败: {}", e)),
                weight: 10,
            },
        }
    }

    /// 验证配置文件
    async fn validate_configuration(&self) -> ValidationCheck {
        let name = "配置文件".to_string();
        let description = "验证系统配置文件是否完整和有效".to_string();

        // 检查配置管理器是否正常工作
        let db_config = self.config_manager.get_database_config();
        match db_config.validate() {
            Ok(_) => {
                // 检查内置提供商配置
                if let Some(builtin_manager) = self.config_manager.get_builtin_provider_manager() {
                    match builtin_manager.lock() {
                        Ok(mut manager) => {
                            let providers =
                                manager.convert_to_model_providers().unwrap_or_default();
                            if providers.is_empty() {
                                ValidationCheck {
                                    name,
                                    passed: false,
                                    description,
                                    error: Some("未找到内置提供商配置".to_string()),
                                    weight: 8,
                                }
                            } else {
                                ValidationCheck {
                                    name,
                                    passed: true,
                                    description,
                                    error: None,
                                    weight: 8,
                                }
                            }
                        }
                        Err(e) => ValidationCheck {
                            name,
                            passed: false,
                            description,
                            error: Some(format!("无法访问内置提供商配置: {}", e)),
                            weight: 8,
                        },
                    }
                } else {
                    ValidationCheck {
                        name,
                        passed: false,
                        description,
                        error: Some("内置提供商配置管理器未初始化".to_string()),
                        weight: 8,
                    }
                }
            }
            Err(e) => ValidationCheck {
                name,
                passed: false,
                description,
                error: Some(format!("配置文件验证失败: {}", e)),
                weight: 8,
            },
        }
    }

    /// 验证数据库表结构
    async fn validate_database_schema(&self) -> ValidationCheck {
        let name = "数据库表结构".to_string();
        let description = "验证所有必需的数据库表是否存在".to_string();

        let required_tables = vec![
            "model_provider",
            "datasource",
            "access_token",
            "connector",
            "mcp_server",
        ];

        for table in &required_tables {
            let query = format!("INFO FOR TABLE {}", table);
            if let Err(_) = self.db.query(&query).await {
                return ValidationCheck {
                    name,
                    passed: false,
                    description,
                    error: Some(format!("表 {} 不存在", table)),
                    weight: 9,
                };
            }
        }

        ValidationCheck {
            name,
            passed: true,
            description,
            error: None,
            weight: 9,
        }
    }

    /// 验证内置提供商
    async fn validate_builtin_providers(&self) -> ValidationCheck {
        let name = "内置提供商".to_string();
        let description = "验证内置模型提供商是否正确导入".to_string();

        // 检查数据库中是否有内置提供商
        let query = "SELECT * FROM model_provider WHERE builtin = true";
        match self.db.query(query).await {
            Ok(mut response) => {
                let providers: Vec<serde_json::Value> = response.take(0).unwrap_or_default();
                if providers.is_empty() {
                    ValidationCheck {
                        name,
                        passed: false,
                        description,
                        error: Some("数据库中未找到内置提供商".to_string()),
                        weight: 7,
                    }
                } else {
                    info!("找到 {} 个内置提供商", providers.len());
                    ValidationCheck {
                        name,
                        passed: true,
                        description,
                        error: None,
                        weight: 7,
                    }
                }
            }
            Err(e) => ValidationCheck {
                name,
                passed: false,
                description,
                error: Some(format!("查询内置提供商失败: {}", e)),
                weight: 7,
            },
        }
    }

    /// 验证数据一致性
    async fn validate_data_consistency(&self) -> ValidationCheck {
        let name = "数据一致性".to_string();
        let description = "验证数据库中数据的一致性和完整性".to_string();

        // 检查是否有重复的提供商名称
        let query = "SELECT name, count() as cnt FROM model_provider GROUP BY name HAVING cnt > 1";
        match self.db.query(query).await {
            Ok(mut response) => {
                let duplicates: Vec<serde_json::Value> = response.take(0).unwrap_or_default();
                if !duplicates.is_empty() {
                    ValidationCheck {
                        name,
                        passed: false,
                        description,
                        error: Some(format!("发现重复的提供商名称: {:?}", duplicates)),
                        weight: 6,
                    }
                } else {
                    ValidationCheck {
                        name,
                        passed: true,
                        description,
                        error: None,
                        weight: 6,
                    }
                }
            }
            Err(e) => ValidationCheck {
                name,
                passed: false,
                description,
                error: Some(format!("数据一致性检查失败: {}", e)),
                weight: 6,
            },
        }
    }

    /// 验证系统资源
    async fn validate_system_resources(&self) -> ValidationCheck {
        let name = "系统资源".to_string();
        let description = "验证系统资源是否充足".to_string();

        // 简单的资源检查，实际项目中可以扩展
        // 这里主要检查数据库响应时间
        let start = std::time::Instant::now();
        match self.db.query("SELECT 1").await {
            Ok(_) => {
                let duration = start.elapsed();
                if duration.as_millis() > 1000 {
                    ValidationCheck {
                        name,
                        passed: false,
                        description,
                        error: Some(format!("数据库响应时间过长: {}ms", duration.as_millis())),
                        weight: 5,
                    }
                } else {
                    ValidationCheck {
                        name,
                        passed: true,
                        description,
                        error: None,
                        weight: 5,
                    }
                }
            }
            Err(e) => ValidationCheck {
                name,
                passed: false,
                description,
                error: Some(format!("系统资源检查失败: {}", e)),
                weight: 5,
            },
        }
    }

    /// 计算总体评分
    fn calculate_score(&self, checks: &[ValidationCheck]) -> u8 {
        let total_weight: u32 = checks.iter().map(|c| c.weight as u32).sum();
        let passed_weight: u32 = checks
            .iter()
            .filter(|c| c.passed)
            .map(|c| c.weight as u32)
            .sum();

        if total_weight == 0 {
            return 0;
        }

        ((passed_weight * 100) / total_weight) as u8
    }

    /// 快速健康检查
    pub async fn quick_health_check(&self) -> Result<bool> {
        // 只检查最关键的项目
        let db_check = self.validate_database_connection().await;
        let schema_check = self.validate_database_schema().await;

        Ok(db_check.passed && schema_check.passed)
    }
}
