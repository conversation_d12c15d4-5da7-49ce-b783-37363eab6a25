use std::{sync::Arc, time::Instant};

use async_trait::async_trait;
use chrono::Utc;
use tracing::{debug, error, info, warn};

use crate::{
    config::{
        builtin_provider_config::BuiltinProviderConfigManager, config_manager::ConfigManager,
    },
    error::{error::CocoError, result::Result},
    models::model_provider::ModelProvider,
    repositories::model_provider_repo::ModelProviderRepository,
    services::import_strategy::{
        ImportAction, ImportDetail, ImportResult, ImportStrategy, ImportStrategyProcessor,
    },
};

/// 提供商导入服务特征
/// 定义内置提供商导入的核心接口
#[async_trait]
pub trait ProviderImportServiceTrait: Send + Sync {
    /// 批量导入所有内置提供商
    async fn import_all_providers(&self, strategy: ImportStrategy) -> Result<ImportResult>;

    /// 导入单个提供商（按ID）
    async fn import_provider_by_id(
        &self,
        provider_id: &str,
        strategy: ImportStrategy,
    ) -> Result<ImportResult>;

    /// 导入单个提供商（按名称）
    async fn import_provider_by_name(
        &self,
        provider_name: &str,
        strategy: ImportStrategy,
    ) -> Result<ImportResult>;

    /// 重新加载配置并导入
    async fn reload_and_import(&self, strategy: ImportStrategy) -> Result<ImportResult>;

    /// 验证导入状态
    async fn validate_import_status(&self) -> Result<ImportValidationResult>;
}

/// 导入验证结果
#[derive(Debug, Clone)]
pub struct ImportValidationResult {
    /// 配置文件中的提供商数量
    pub config_provider_count: usize,
    /// 数据库中的内置提供商数量
    pub database_builtin_count: usize,
    /// 缺失的提供商列表
    pub missing_providers: Vec<String>,
    /// 过期的提供商列表（配置中不存在但数据库中存在）
    pub outdated_providers: Vec<String>,
    /// 验证是否通过
    pub validation_passed: bool,
}

/// 提供商导入服务实现
/// 负责管理内置提供商的导入、更新和验证
#[derive(Clone)]
pub struct ProviderImportService {
    /// 模型提供商仓储
    repository: Arc<dyn ModelProviderRepository>,
    /// 配置管理器
    config_manager: Arc<ConfigManager>,
}

impl ProviderImportService {
    /// 创建新的提供商导入服务
    pub fn new(
        repository: Arc<dyn ModelProviderRepository>,
        config_manager: Arc<ConfigManager>,
    ) -> Self {
        Self {
            repository,
            config_manager,
        }
    }

    /// 获取内置提供商配置管理器
    fn get_builtin_config_manager(
        &self,
    ) -> Result<Arc<std::sync::Mutex<BuiltinProviderConfigManager>>> {
        self.config_manager
            .get_builtin_provider_manager()
            .ok_or_else(|| CocoError::ConfigError("内置提供商配置管理器未初始化".to_string()))
    }

    /// 加载配置文件中的所有提供商
    async fn load_config_providers(&self) -> Result<Vec<ModelProvider>> {
        let builtin_manager = self.get_builtin_config_manager()?;

        let providers = {
            let mut manager = builtin_manager
                .lock()
                .map_err(|e| CocoError::ConfigError(format!("无法访问内置提供商配置: {}", e)))?;

            manager
                .convert_to_model_providers()
                .map_err(|e| CocoError::ConfigError(format!("转换内置提供商配置失败: {}", e)))?
        };

        info!("从配置文件加载了 {} 个内置提供商", providers.len());
        Ok(providers)
    }

    /// 查找配置中的提供商（按ID）
    async fn find_config_provider_by_id(&self, provider_id: &str) -> Result<Option<ModelProvider>> {
        let providers = self.load_config_providers().await?;
        Ok(providers.into_iter().find(|p| p.id == provider_id))
    }

    /// 查找配置中的提供商（按名称）
    async fn find_config_provider_by_name(
        &self,
        provider_name: &str,
    ) -> Result<Option<ModelProvider>> {
        let providers = self.load_config_providers().await?;
        Ok(providers.into_iter().find(|p| p.name == provider_name))
    }

    /// 处理单个提供商的导入
    async fn process_single_provider(
        &self,
        config_provider: &ModelProvider,
        processor: &ImportStrategyProcessor,
    ) -> ImportDetail {
        let start_time = Instant::now();
        let timestamp = Utc::now();

        // 检查数据库中是否已存在
        let existing_provider = match self.repository.get_by_id(&config_provider.id).await {
            Ok(provider) => provider,
            Err(e) => {
                error!("检查提供商 {} 时出错: {}", config_provider.name, e);
                return ImportDetail {
                    provider_id: config_provider.id.clone(),
                    provider_name: config_provider.name.clone(),
                    action: ImportAction::Failed,
                    success: false,
                    error: Some(format!("检查提供商失败: {}", e)),
                    duration_ms: start_time.elapsed().as_millis() as u64,
                    timestamp,
                };
            }
        };

        // 根据策略处理提供商
        let processed_provider =
            match processor.process_provider(config_provider, existing_provider.as_ref()) {
                Ok(Some(provider)) => provider,
                Ok(None) => {
                    // 跳过此提供商
                    debug!("跳过提供商: {}", config_provider.name);
                    return ImportDetail {
                        provider_id: config_provider.id.clone(),
                        provider_name: config_provider.name.clone(),
                        action: ImportAction::Skipped,
                        success: true,
                        error: None,
                        duration_ms: start_time.elapsed().as_millis() as u64,
                        timestamp,
                    };
                }
                Err(e) => {
                    error!("处理提供商 {} 失败: {}", config_provider.name, e);
                    return ImportDetail {
                        provider_id: config_provider.id.clone(),
                        provider_name: config_provider.name.clone(),
                        action: ImportAction::Failed,
                        success: false,
                        error: Some(format!("处理提供商失败: {}", e)),
                        duration_ms: start_time.elapsed().as_millis() as u64,
                        timestamp,
                    };
                }
            };

        // 保存到数据库
        let (action, success, error) = if existing_provider.is_some() {
            // 更新现有提供商
            match self.repository.update(&processed_provider).await {
                Ok(_) => {
                    info!("更新提供商成功: {}", processed_provider.name);
                    (ImportAction::Updated, true, None)
                }
                Err(e) => {
                    error!("更新提供商 {} 失败: {}", processed_provider.name, e);
                    (
                        ImportAction::Failed,
                        false,
                        Some(format!("更新失败: {}", e)),
                    )
                }
            }
        } else {
            // 创建新提供商
            match self.repository.create(&processed_provider).await {
                Ok(_) => {
                    info!("创建提供商成功: {}", processed_provider.name);
                    (ImportAction::Created, true, None)
                }
                Err(e) => {
                    error!("创建提供商 {} 失败: {}", processed_provider.name, e);
                    (
                        ImportAction::Failed,
                        false,
                        Some(format!("创建失败: {}", e)),
                    )
                }
            }
        };

        ImportDetail {
            provider_id: config_provider.id.clone(),
            provider_name: config_provider.name.clone(),
            action,
            success,
            error,
            duration_ms: start_time.elapsed().as_millis() as u64,
            timestamp,
        }
    }
}

#[async_trait]
impl ProviderImportServiceTrait for ProviderImportService {
    /// 批量导入所有内置提供商
    async fn import_all_providers(&self, strategy: ImportStrategy) -> Result<ImportResult> {
        info!("开始批量导入内置提供商，策略: {}", strategy);

        let mut result = ImportResult::new(strategy.clone());
        let processor = ImportStrategyProcessor::new(strategy);

        // 加载配置文件中的所有提供商
        let config_providers = match self.load_config_providers().await {
            Ok(providers) => providers,
            Err(e) => {
                error!("加载配置文件失败: {}", e);
                result.finish();
                return Err(e);
            }
        };

        if config_providers.is_empty() {
            warn!("配置文件中没有找到内置提供商");
            result.finish();
            return Ok(result);
        }

        info!("准备导入 {} 个内置提供商", config_providers.len());

        // 逐个处理提供商
        for config_provider in &config_providers {
            let detail = self
                .process_single_provider(config_provider, &processor)
                .await;
            result.add_detail(detail);
        }

        result.finish();

        info!(
            "批量导入完成: 成功 {}, 更新 {}, 跳过 {}, 失败 {}, 耗时 {}ms",
            result.imported_count,
            result.updated_count,
            result.skipped_count,
            result.failed_count,
            result.total_duration_ms
        );

        Ok(result)
    }

    /// 导入单个提供商（按ID）
    async fn import_provider_by_id(
        &self,
        provider_id: &str,
        strategy: ImportStrategy,
    ) -> Result<ImportResult> {
        info!("开始导入提供商 (ID: {}), 策略: {}", provider_id, strategy);

        let mut result = ImportResult::new(strategy.clone());
        let processor = ImportStrategyProcessor::new(strategy);

        // 查找配置中的提供商
        let config_provider = match self.find_config_provider_by_id(provider_id).await? {
            Some(provider) => provider,
            None => {
                let error_msg = format!("配置文件中未找到ID为 {} 的提供商", provider_id);
                warn!("{}", error_msg);
                result.finish();
                return Err(CocoError::NotFound(error_msg));
            }
        };

        // 处理提供商导入
        let detail = self
            .process_single_provider(&config_provider, &processor)
            .await;
        result.add_detail(detail);
        result.finish();

        info!("单个提供商导入完成: {}", provider_id);
        Ok(result)
    }

    /// 导入单个提供商（按名称）
    async fn import_provider_by_name(
        &self,
        provider_name: &str,
        strategy: ImportStrategy,
    ) -> Result<ImportResult> {
        info!(
            "开始导入提供商 (名称: {}), 策略: {}",
            provider_name, strategy
        );

        let mut result = ImportResult::new(strategy.clone());
        let processor = ImportStrategyProcessor::new(strategy);

        // 查找配置中的提供商
        let config_provider = match self.find_config_provider_by_name(provider_name).await? {
            Some(provider) => provider,
            None => {
                let error_msg = format!("配置文件中未找到名称为 {} 的提供商", provider_name);
                warn!("{}", error_msg);
                result.finish();
                return Err(CocoError::NotFound(error_msg));
            }
        };

        // 处理提供商导入
        let detail = self
            .process_single_provider(&config_provider, &processor)
            .await;
        result.add_detail(detail);
        result.finish();

        info!("单个提供商导入完成: {}", provider_name);
        Ok(result)
    }

    /// 重新加载配置并导入
    async fn reload_and_import(&self, strategy: ImportStrategy) -> Result<ImportResult> {
        info!("重新加载配置并导入，策略: {}", strategy);

        // 重新加载配置
        let builtin_manager = self.get_builtin_config_manager()?;
        {
            let mut manager = builtin_manager
                .lock()
                .map_err(|e| CocoError::ConfigError(format!("无法访问内置提供商配置: {}", e)))?;

            manager
                .load_config()
                .map_err(|e| CocoError::ConfigError(format!("重新加载配置失败: {}", e)))?;
        }

        // 执行导入
        self.import_all_providers(strategy).await
    }

    /// 验证导入状态
    async fn validate_import_status(&self) -> Result<ImportValidationResult> {
        info!("开始验证导入状态");

        // 获取配置文件中的提供商
        let config_providers = self.load_config_providers().await?;
        let config_provider_names: std::collections::HashSet<String> =
            config_providers.iter().map(|p| p.name.clone()).collect();

        // 获取数据库中的内置提供商
        let db_builtin_providers = self.repository.get_builtin().await?;
        let db_provider_names: std::collections::HashSet<String> = db_builtin_providers
            .iter()
            .map(|p| p.name.clone())
            .collect();

        // 找出缺失的提供商（配置中有但数据库中没有）
        let missing_providers: Vec<String> = config_provider_names
            .difference(&db_provider_names)
            .cloned()
            .collect();

        // 找出过期的提供商（数据库中有但配置中没有）
        let outdated_providers: Vec<String> = db_provider_names
            .difference(&config_provider_names)
            .cloned()
            .collect();

        let validation_passed = missing_providers.is_empty() && outdated_providers.is_empty();

        let result = ImportValidationResult {
            config_provider_count: config_providers.len(),
            database_builtin_count: db_builtin_providers.len(),
            missing_providers,
            outdated_providers,
            validation_passed,
        };

        if result.validation_passed {
            info!("导入状态验证通过");
        } else {
            warn!("导入状态验证失败，存在不一致");
        }

        Ok(result)
    }
}

#[cfg(test)]
mod tests {
    use chrono::Utc;

    use super::*;

    fn create_test_provider(id: &str, name: &str, enabled: bool) -> ModelProvider {
        ModelProvider {
            id: id.to_string(),
            created: Utc::now(),
            updated: Utc::now(),
            name: name.to_string(),
            api_key: "".to_string(),
            api_type: "openai".to_string(),
            base_url: "https://api.example.com".to_string(),
            icon: "test_icon".to_string(),
            models: vec![],
            enabled,
            builtin: true,
            description: "测试提供商".to_string(),
        }
    }

    #[tokio::test]
    async fn test_import_strategy_integration() {
        // 测试不同的导入策略
        let processor = ImportStrategyProcessor::new(ImportStrategy::UpdateExisting);

        let mut existing = create_test_provider("test-1", "Test Provider", false);
        existing.api_key = "existing-key".to_string();

        let new_provider = create_test_provider("test-1", "Updated Provider", true);

        let result = processor.process_provider(&new_provider, Some(&existing));
        assert!(result.is_ok());

        let updated = result.unwrap().unwrap();
        assert_eq!(updated.name, "Updated Provider"); // 名称应该更新
        assert_eq!(updated.api_key, "existing-key"); // API密钥应该保留
        assert_eq!(updated.enabled, false); // enabled状态应该保留
    }
}
