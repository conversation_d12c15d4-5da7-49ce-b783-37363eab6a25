use std::{
    path::{Path, PathBuf},
    sync::{
        atomic::{AtomicBool, Ordering},
        Arc,
    },
    time::{Duration, SystemTime},
};

use notify::{Event, EventKind, RecommendedWatcher, RecursiveMode, Watcher};
use tokio::sync::mpsc;
use tracing::{debug, error, info, warn};

use crate::error::{error::CocoError, result::Result};

/// 文件变更事件类型
#[derive(Debug, Clone)]
pub enum FileChangeEvent {
    /// 文件被修改
    Modified(PathBuf),
    /// 文件被创建
    Created(PathBuf),
    /// 文件被删除
    Deleted(PathBuf),
    /// 文件被重命名
    Renamed(PathBuf, PathBuf),
}

/// 文件监控器
pub struct FileWatcher {
    /// 监控的文件路径
    file_path: PathBuf,
    /// notify监控器
    _watcher: RecommendedWatcher,
    /// 是否正在运行
    is_running: Arc<AtomicBool>,
    /// 最后修改时间（用于防抖）
    last_modified: Arc<std::sync::Mutex<Option<SystemTime>>>,
    /// 防抖延迟
    debounce_delay: Duration,
}

impl FileWatcher {
    /// 创建新的文件监控器
    pub fn new<P, F>(file_path: P, callback: F) -> Result<Self>
    where
        P: AsRef<Path>,
        F: Fn(&Path) + Send + Sync + 'static,
    {
        let file_path = file_path.as_ref().to_path_buf();
        let is_running = Arc::new(AtomicBool::new(false));
        let last_modified = Arc::new(std::sync::Mutex::new(None));
        let debounce_delay = Duration::from_millis(300);

        // 创建事件通道
        let (tx, mut rx) = mpsc::unbounded_channel::<FileChangeEvent>();

        // 克隆用于闭包的变量
        let file_path_clone = file_path.clone();
        let is_running_clone = is_running.clone();
        let last_modified_clone = last_modified.clone();

        // 创建notify监控器
        let watcher = RecommendedWatcher::new(
            move |res: notify::Result<Event>| {
                match res {
                    Ok(event) => {
                        // 检查事件是否与目标文件相关
                        let is_target_file = event.paths.iter().any(|path| {
                            path == &file_path_clone
                                || path.file_name() == file_path_clone.file_name()
                        });

                        if !is_target_file {
                            return;
                        }

                        // 防抖处理
                        if let Ok(mut last_mod) = last_modified_clone.lock() {
                            let now = SystemTime::now();
                            if let Some(last_time) = *last_mod {
                                if now.duration_since(last_time).unwrap_or_default()
                                    < Duration::from_millis(100)
                                {
                                    debug!("文件变更事件被防抖过滤");
                                    return;
                                }
                            }
                            *last_mod = Some(now);
                        }

                        // 处理不同类型的事件
                        let change_event = match event.kind {
                            EventKind::Modify(_) => {
                                debug!("检测到文件修改: {:?}", event.paths);
                                Some(FileChangeEvent::Modified(file_path_clone.clone()))
                            }
                            EventKind::Create(_) => {
                                debug!("检测到文件创建: {:?}", event.paths);
                                Some(FileChangeEvent::Created(file_path_clone.clone()))
                            }
                            EventKind::Remove(_) => {
                                debug!("检测到文件删除: {:?}", event.paths);
                                Some(FileChangeEvent::Deleted(file_path_clone.clone()))
                            }
                            _ => {
                                debug!("检测到其他文件事件: {:?}", event.kind);
                                None
                            }
                        };

                        // 发送事件
                        if let Some(event) = change_event {
                            if let Err(e) = tx.send(event) {
                                error!("发送文件变更事件失败: {}", e);
                            }
                        }
                    }
                    Err(e) => {
                        error!("文件监控错误: {}", e);
                    }
                }
            },
            notify::Config::default(),
        )
        .map_err(|e| CocoError::ConfigError(format!("创建文件监控器失败: {}", e)))?;

        // 启动事件处理任务
        let callback = Arc::new(callback);
        let is_running_task = is_running.clone();
        tokio::spawn(async move {
            is_running_task.store(true, Ordering::SeqCst);

            while let Some(event) = rx.recv().await {
                if !is_running_task.load(Ordering::SeqCst) {
                    break;
                }

                match event {
                    FileChangeEvent::Modified(path) => {
                        info!("文件已修改: {:?}", path);
                        callback(&path);
                    }
                    FileChangeEvent::Created(path) => {
                        info!("文件已创建: {:?}", path);
                        callback(&path);
                    }
                    FileChangeEvent::Deleted(path) => {
                        warn!("文件已删除: {:?}", path);
                        // 对于删除事件，我们可能需要特殊处理
                    }
                    FileChangeEvent::Renamed(old_path, new_path) => {
                        info!("文件已重命名: {:?} -> {:?}", old_path, new_path);
                        callback(&new_path);
                    }
                }
            }

            is_running_task.store(false, Ordering::SeqCst);
            debug!("文件监控事件处理任务已停止");
        });

        Ok(Self {
            file_path,
            _watcher: watcher,
            is_running,
            last_modified,
            debounce_delay,
        })
    }

    /// 开始监控文件
    pub fn start_watching(&mut self) -> Result<()> {
        if self.is_running.load(Ordering::SeqCst) {
            warn!("文件监控已经在运行");
            return Ok(());
        }

        // 确保文件存在
        if !self.file_path.exists() {
            return Err(CocoError::ConfigError(format!(
                "要监控的文件不存在: {:?}",
                self.file_path
            )));
        }

        // 开始监控文件或其父目录
        let watch_path = if self.file_path.is_file() {
            // 监控文件的父目录，因为某些编辑器会先删除再创建文件
            self.file_path.parent().unwrap_or(&self.file_path)
        } else {
            &self.file_path
        };

        self._watcher
            .watch(watch_path, RecursiveMode::NonRecursive)
            .map_err(|e| CocoError::ConfigError(format!("开始监控文件失败: {}", e)))?;

        self.is_running.store(true, Ordering::SeqCst);
        info!("开始监控文件: {:?}", self.file_path);
        Ok(())
    }

    /// 停止监控文件
    pub fn stop_watching(&mut self) -> Result<()> {
        if !self.is_running.load(Ordering::SeqCst) {
            return Ok(());
        }

        // 停止监控
        let watch_path = if self.file_path.is_file() {
            self.file_path.parent().unwrap_or(&self.file_path)
        } else {
            &self.file_path
        };

        self._watcher
            .unwatch(watch_path)
            .map_err(|e| CocoError::ConfigError(format!("停止监控文件失败: {}", e)))?;

        self.is_running.store(false, Ordering::SeqCst);
        info!("停止监控文件: {:?}", self.file_path);
        Ok(())
    }

    /// 检查是否正在监控
    pub fn is_watching(&self) -> bool {
        self.is_running.load(Ordering::SeqCst)
    }

    /// 获取监控的文件路径
    pub fn get_file_path(&self) -> &Path {
        &self.file_path
    }

    /// 设置防抖延迟
    pub fn set_debounce_delay(&mut self, delay: Duration) {
        self.debounce_delay = delay;
        debug!("设置防抖延迟: {:?}", delay);
    }

    /// 获取防抖延迟
    pub fn get_debounce_delay(&self) -> Duration {
        self.debounce_delay
    }

    /// 手动触发文件检查
    pub fn trigger_check(&self) {
        if self.file_path.exists() {
            info!("手动触发文件检查: {:?}", self.file_path);
            // 这里可以添加手动检查逻辑
        } else {
            warn!("要检查的文件不存在: {:?}", self.file_path);
        }
    }
}

impl Drop for FileWatcher {
    fn drop(&mut self) {
        if self.is_running.load(Ordering::SeqCst) {
            let _ = self.stop_watching();
        }
        debug!("文件监控器已销毁: {:?}", self.file_path);
    }
}

/// 文件监控器构建器
pub struct FileWatcherBuilder {
    file_path: Option<PathBuf>,
    debounce_delay: Duration,
}

impl FileWatcherBuilder {
    /// 创建新的构建器
    pub fn new() -> Self {
        Self {
            file_path: None,
            debounce_delay: Duration::from_millis(300),
        }
    }

    /// 设置要监控的文件路径
    pub fn file_path<P: AsRef<Path>>(mut self, path: P) -> Self {
        self.file_path = Some(path.as_ref().to_path_buf());
        self
    }

    /// 设置防抖延迟
    pub fn debounce_delay(mut self, delay: Duration) -> Self {
        self.debounce_delay = delay;
        self
    }

    /// 构建文件监控器
    pub fn build<F>(self, callback: F) -> Result<FileWatcher>
    where
        F: Fn(&Path) + Send + Sync + 'static,
    {
        let file_path = self
            .file_path
            .ok_or_else(|| CocoError::ConfigError("未设置文件路径".to_string()))?;

        let mut watcher = FileWatcher::new(file_path, callback)?;
        watcher.set_debounce_delay(self.debounce_delay);
        Ok(watcher)
    }
}

impl Default for FileWatcherBuilder {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use std::sync::atomic::AtomicU32;

    use tempfile::NamedTempFile;
    use tokio::time::sleep;

    use super::*;

    #[tokio::test]
    async fn test_file_watcher_creation() {
        let temp_file = NamedTempFile::new().unwrap();
        let counter = Arc::new(AtomicU32::new(0));
        let counter_clone = counter.clone();

        let _watcher = FileWatcher::new(temp_file.path(), move |_path| {
            counter_clone.fetch_add(1, Ordering::SeqCst);
        });

        assert!(_watcher.is_ok());
    }

    #[tokio::test]
    async fn test_file_watcher_builder() {
        let temp_file = NamedTempFile::new().unwrap();
        let counter = Arc::new(AtomicU32::new(0));
        let counter_clone = counter.clone();

        let watcher = FileWatcherBuilder::new()
            .file_path(temp_file.path())
            .debounce_delay(Duration::from_millis(100))
            .build(move |_path| {
                counter_clone.fetch_add(1, Ordering::SeqCst);
            });

        assert!(watcher.is_ok());
        let watcher = watcher.unwrap();
        assert_eq!(watcher.get_debounce_delay(), Duration::from_millis(100));
    }
}
