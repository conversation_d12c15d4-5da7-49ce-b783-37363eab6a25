use std::sync::Arc;

use surrealdb::{engine::remote::ws::Client, Surreal};
use tracing::{error, info, warn};

use crate::{
    config::config_manager::Config<PERSON>anager,
    error::{error::CocoError, result::Result},
    models::model_provider::ModelProvider,
    repositories::model_provider_repo::ModelProviderRepository,
    services::{
        database_setup::DatabaseSetupService,
        system_validator::{SystemValidationResult, SystemValidator},
    },
};

/// 初始化错误类型
#[derive(Debug, thiserror::Error)]
pub enum InitError {
    #[error("配置文件错误: {0}")]
    ConfigError(String),

    #[error("数据库连接失败: {0}")]
    DatabaseError(String),

    #[error("提供商导入失败: {0}")]
    ProviderImportError(String),

    #[error("系统验证失败: {0}")]
    ValidationError(String),

    #[error("初始化服务错误: {0}")]
    ServiceError(String),
}

impl From<CocoError> for InitError {
    fn from(err: CocoError) -> Self {
        match err {
            CocoError::Database(msg) => InitError::DatabaseError(msg),
            CocoError::ConfigError(msg) => InitError::ConfigError(msg),
            _ => InitError::ServiceError(err.to_string()),
        }
    }
}

/// 初始化结果
#[derive(Debug, Clone)]
pub struct InitializationResult {
    /// 初始化是否成功
    pub success: bool,
    /// 验证结果
    pub validation_result: Option<SystemValidationResult>,
    /// 导入的内置提供商数量
    pub imported_providers: usize,
    /// 错误信息
    pub errors: Vec<String>,
    /// 警告信息
    pub warnings: Vec<String>,
    /// 初始化耗时（毫秒）
    pub duration_ms: u64,
}

/// 系统初始化服务
///
/// 负责系统启动时的完整初始化流程
#[derive(Clone)]
pub struct InitializationService {
    /// 数据库连接
    db: Arc<Surreal<Client>>,
    /// 配置管理器
    config_manager: Arc<ConfigManager>,
    /// 模型提供商仓储
    model_provider_repo: Arc<dyn ModelProviderRepository>,
    /// 数据库设置服务
    database_setup: DatabaseSetupService,
    /// 系统验证器
    system_validator: SystemValidator,
}

impl InitializationService {
    /// 创建新的初始化服务
    pub fn new(
        db: Arc<Surreal<Client>>,
        config_manager: Arc<ConfigManager>,
        model_provider_repo: Arc<dyn ModelProviderRepository>,
    ) -> Self {
        let database_setup = DatabaseSetupService::new(db.clone());
        let system_validator = SystemValidator::new(
            db.clone(),
            config_manager.clone(),
            model_provider_repo.clone(),
        );

        Self {
            db,
            config_manager,
            model_provider_repo,
            database_setup,
            system_validator,
        }
    }

    /// 执行完整的系统初始化
    pub async fn initialize_system(&self) -> std::result::Result<InitializationResult, InitError> {
        let start_time = std::time::Instant::now();
        info!("开始系统初始化...");

        let mut errors = Vec::new();
        let mut warnings = Vec::new();
        let mut imported_providers = 0;

        // 1. 检查数据库连接
        if let Err(e) = self.check_database_connection().await {
            let error_msg = format!("数据库连接检查失败: {}", e);
            error!("{}", error_msg);
            errors.push(error_msg);

            // 尝试恢复
            if let Err(recovery_err) = self.handle_database_error(e).await {
                return Err(recovery_err);
            }
        }

        // 2. 创建必要的表结构
        if let Err(e) = self.ensure_database_schema().await {
            let error_msg = format!("数据库表结构创建失败: {}", e);
            error!("{}", error_msg);
            errors.push(error_msg.clone());
            return Err(InitError::DatabaseError(error_msg));
        }

        // 3. 导入内置提供商
        match self.import_builtin_providers().await {
            Ok(count) => {
                imported_providers = count;
                info!("成功导入 {} 个内置提供商", count);
            }
            Err(e) => {
                let warning_msg = format!("内置提供商导入部分失败: {}", e);
                warn!("{}", warning_msg);
                warnings.push(warning_msg);
                // 内置提供商导入失败不应该阻止系统启动
            }
        }

        // 4. 验证系统状态
        let validation_result = match self.validate_system_state().await {
            Ok(result) => {
                if !result.passed {
                    let error_msg = format!("系统验证失败，评分: {}/100", result.score);
                    warn!("{}", error_msg);
                    warnings.push(error_msg);
                }
                Some(result)
            }
            Err(e) => {
                let error_msg = format!("系统验证执行失败: {}", e);
                error!("{}", error_msg);
                errors.push(error_msg);
                None
            }
        };

        let duration_ms = start_time.elapsed().as_millis() as u64;
        let success = errors.is_empty();

        let result = InitializationResult {
            success,
            validation_result,
            imported_providers,
            errors,
            warnings,
            duration_ms,
        };

        if result.success {
            info!("系统初始化完成，耗时: {}ms", result.duration_ms);
        } else {
            error!("系统初始化失败，耗时: {}ms", result.duration_ms);
        }

        Ok(result)
    }

    /// 检查数据库连接
    async fn check_database_connection(&self) -> Result<()> {
        info!("检查数据库连接...");
        self.database_setup.check_database_connection().await
    }

    /// 创建必要的表结构
    async fn ensure_database_schema(&self) -> Result<()> {
        info!("确保数据库表结构...");
        self.database_setup.ensure_database_schema().await?;
        self.database_setup.validate_schema().await
    }

    /// 导入内置提供商
    async fn import_builtin_providers(&self) -> std::result::Result<usize, InitError> {
        info!("开始导入内置提供商...");

        let builtin_manager = self
            .config_manager
            .get_builtin_provider_manager()
            .ok_or_else(|| InitError::ConfigError("内置提供商配置管理器未初始化".to_string()))?;

        let providers = {
            let mut manager = builtin_manager
                .lock()
                .map_err(|e| InitError::ConfigError(format!("无法访问内置提供商配置: {}", e)))?;
            manager
                .convert_to_model_providers()
                .map_err(|e| InitError::ConfigError(format!("转换内置提供商配置失败: {}", e)))?
        };

        if providers.is_empty() {
            warn!("未找到内置提供商配置");
            return Ok(0);
        }

        let mut imported_count = 0;
        let mut failed_count = 0;

        for provider_config in providers {
            match self.process_builtin_provider(&provider_config).await {
                Ok(_) => {
                    imported_count += 1;
                    info!("导入内置提供商成功: {}", provider_config.name);
                }
                Err(e) => {
                    failed_count += 1;
                    warn!("导入内置提供商失败: {} - {}", provider_config.name, e);
                }
            }
        }

        info!(
            "内置提供商导入完成: 成功 {}, 失败 {}",
            imported_count, failed_count
        );

        Ok(imported_count)
    }

    /// 处理单个内置提供商
    async fn process_builtin_provider(
        &self,
        config: &ModelProvider,
    ) -> std::result::Result<(), InitError> {
        // 检查是否已存在
        match self.model_provider_repo.get_by_id(&config.id).await {
            Ok(Some(existing)) => {
                // 更新策略：保留用户修改的敏感字段
                self.update_existing_builtin(existing, config).await
            }
            Ok(None) => {
                // 创建新的内置提供商
                self.create_new_builtin(config).await
            }
            Err(e) => Err(InitError::ProviderImportError(format!(
                "检查提供商 {} 时出错: {}",
                config.id, e
            ))),
        }
    }

    /// 更新现有的内置提供商
    async fn update_existing_builtin(
        &self,
        mut existing: ModelProvider,
        config: &ModelProvider,
    ) -> std::result::Result<(), InitError> {
        // 保留用户可能修改的字段（如api_key）
        let original_api_key = existing.api_key.clone();
        let original_enabled = existing.enabled;

        // 更新配置中的字段
        existing.name = config.name.clone();
        existing.api_type = config.api_type.clone();
        existing.base_url = config.base_url.clone();
        existing.icon = config.icon.clone();
        existing.models = config.models.clone();
        existing.description = config.description.clone();
        existing.builtin = true;

        // 如果原来的api_key不为空且不是默认值，则保留
        if !original_api_key.is_empty() && original_api_key != config.api_key {
            info!("保留提供商 {} 的现有API密钥", config.name);
        } else {
            existing.api_key = config.api_key.clone();
        }

        // 保留用户的启用/禁用设置
        existing.enabled = original_enabled;

        // 更新时间戳
        existing.updated = chrono::Utc::now();

        self.model_provider_repo
            .update(&existing)
            .await
            .map_err(|e| {
                InitError::ProviderImportError(format!(
                    "更新内置提供商 {} 失败: {}",
                    config.name, e
                ))
            })?;

        Ok(())
    }

    /// 创建新的内置提供商
    async fn create_new_builtin(
        &self,
        config: &ModelProvider,
    ) -> std::result::Result<(), InitError> {
        let now = chrono::Utc::now();
        let provider = ModelProvider {
            id: config.id.clone(),
            created: now,
            updated: now,
            name: config.name.clone(),
            api_key: config.api_key.clone(),
            api_type: config.api_type.clone(),
            base_url: config.base_url.clone(),
            icon: config.icon.clone(),
            models: config.models.clone(),
            enabled: config.enabled,
            builtin: true,
            description: config.description.clone(),
        };

        self.model_provider_repo
            .create(&provider)
            .await
            .map_err(|e| {
                InitError::ProviderImportError(format!(
                    "创建内置提供商 {} 失败: {}",
                    config.name, e
                ))
            })?;

        Ok(())
    }

    /// 验证系统状态
    async fn validate_system_state(&self) -> Result<SystemValidationResult> {
        info!("验证系统状态...");
        self.system_validator.validate_system().await
    }

    /// 处理数据库错误
    async fn handle_database_error(&self, error: CocoError) -> std::result::Result<(), InitError> {
        error!("处理数据库错误: {}", error);

        // 这里可以实现错误恢复逻辑
        // 例如：重试连接、使用备用配置等
        // 目前简单地返回错误

        Err(InitError::DatabaseError(error.to_string()))
    }

    /// 快速初始化检查（用于健康检查）
    pub async fn quick_initialization_check(&self) -> std::result::Result<bool, InitError> {
        // 只检查最关键的项目
        match self.system_validator.quick_health_check().await {
            Ok(healthy) => Ok(healthy),
            Err(e) => Err(InitError::ValidationError(e.to_string())),
        }
    }
}
