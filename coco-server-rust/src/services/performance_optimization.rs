use std::{
    collections::HashMap,
    sync::Arc,
    time::{Duration, Instant},
};

use tokio::sync::RwLock;
use tracing::{debug, info, warn};

use crate::{
    error::error::CocoError,
    models::model_provider::ModelProvider,
    monitoring::performance_metrics::{MetricType, PerformanceMetrics},
    repositories::model_provider_repo::{ModelProviderRepository, SearchQuery, SearchResponse},
    services::cache_service::CacheService,
};

/// 性能优化服务
///
/// 提供各种性能优化功能，包括查询优化、缓存策略、批量操作等
pub struct PerformanceOptimizationService {
    /// 数据访问层
    repository: Arc<dyn ModelProviderRepository>,
    /// 缓存服务
    cache_service: Arc<CacheService>,
    /// 性能指标收集器
    metrics: Arc<PerformanceMetrics>,
    /// 查询结果缓存
    query_cache: Arc<RwLock<HashMap<String, (SearchResponse, Instant)>>>,
    /// 缓存TTL
    cache_ttl: Duration,
}

impl PerformanceOptimizationService {
    /// 创建新的性能优化服务
    pub fn new(
        repository: Arc<dyn ModelProviderRepository>,
        cache_service: Arc<CacheService>,
        metrics: Arc<PerformanceMetrics>,
    ) -> Self {
        Self {
            repository,
            cache_service,
            metrics,
            query_cache: Arc::new(RwLock::new(HashMap::new())),
            cache_ttl: Duration::from_secs(1800), // 30分钟
        }
    }

    /// 优化的批量获取操作
    ///
    /// 使用并发查询和缓存来提高批量获取的性能
    pub async fn batch_get_providers(
        &self,
        ids: &[String],
    ) -> Result<Vec<Option<ModelProvider>>, CocoError> {
        let start_time = Instant::now();

        // 分离缓存命中和未命中的ID
        let mut cached_results = HashMap::new();
        let mut uncached_ids = Vec::new();

        for id in ids {
            if let Some(provider) = self.get_from_cache(id).await {
                cached_results.insert(id.clone(), Some(provider));
            } else {
                uncached_ids.push(id.clone());
            }
        }

        // 并发查询未缓存的数据
        let mut handles = Vec::new();
        for id in uncached_ids {
            let repository = self.repository.clone();
            let handle = tokio::spawn(async move { (id.clone(), repository.get_by_id(&id).await) });
            handles.push(handle);
        }

        // 收集查询结果
        let mut db_results = HashMap::new();
        for handle in handles {
            if let Ok((id, result)) = handle.await {
                match result {
                    Ok(provider) => {
                        if let Some(provider) = &provider {
                            self.cache_provider(provider).await;
                        }
                        db_results.insert(id, provider);
                    }
                    Err(e) => {
                        warn!("批量获取提供商失败: id={}, error={}", id, e);
                        db_results.insert(id, None);
                    }
                }
            }
        }

        // 合并结果
        let mut results = Vec::new();
        for id in ids {
            if let Some(provider) = cached_results.get(id) {
                results.push(provider.clone());
            } else if let Some(provider) = db_results.get(id) {
                results.push(provider.clone());
            } else {
                results.push(None);
            }
        }

        // 记录性能指标
        let duration = start_time.elapsed();
        self.metrics
            .record_api_response_time("batch_get_providers", "GET", duration, 200)
            .await;

        // 计算缓存命中率
        let cache_hits = cached_results.len();
        let total_requests = ids.len();
        let hit_rate = cache_hits as f64 / total_requests as f64;
        self.metrics
            .record_cache_hit_rate("model_provider", hit_rate)
            .await;

        info!(
            "批量获取完成: 总数={}, 缓存命中={}, 数据库查询={}, 耗时={:?}",
            total_requests,
            cache_hits,
            db_results.len(),
            duration
        );

        Ok(results)
    }

    /// 优化的搜索操作
    ///
    /// 使用查询缓存和索引优化来提高搜索性能
    pub async fn optimized_search(&self, query: &SearchQuery) -> Result<SearchResponse, CocoError> {
        let start_time = Instant::now();

        // 生成查询缓存键
        let cache_key = self.generate_search_cache_key(query);

        // 检查查询缓存
        if let Some(cached_result) = self.get_search_from_cache(&cache_key).await {
            let duration = start_time.elapsed();
            self.metrics
                .record_api_response_time("search_providers_cached", "POST", duration, 200)
                .await;
            self.metrics
                .record_cache_hit_rate("search_query", 1.0)
                .await;

            debug!("搜索缓存命中: key={}, 耗时={:?}", cache_key, duration);
            return Ok(cached_result);
        }

        // 执行数据库搜索
        let db_start = Instant::now();
        let result = self.repository.search(query).await?;
        let db_duration = db_start.elapsed();

        // 缓存搜索结果
        self.cache_search_result(&cache_key, &result).await;

        // 记录性能指标
        let total_duration = start_time.elapsed();
        self.metrics
            .record_api_response_time("search_providers", "POST", total_duration, 200)
            .await;
        self.metrics
            .record_database_query_time("search", "model_provider", db_duration)
            .await;
        self.metrics
            .record_cache_hit_rate("search_query", 0.0)
            .await;

        info!(
            "搜索完成: 查询={:?}, 结果数={}, 数据库耗时={:?}, 总耗时={:?}",
            query,
            result.hits.hits.len(),
            db_duration,
            total_duration
        );

        Ok(result)
    }

    /// 预热缓存
    ///
    /// 在系统启动时预热热点数据
    pub async fn warmup_cache(&self) -> Result<(), CocoError> {
        info!("开始预热模型提供商缓存");
        let start_time = Instant::now();

        // 获取所有启用的提供商
        let mut enabled_filters = HashMap::new();
        enabled_filters.insert("enabled".to_string(), serde_json::Value::Bool(true));

        let enabled_query = SearchQuery {
            q: None,
            size: Some(100),
            from: Some(0),
            sort: None,
            filters: Some(enabled_filters),
        };

        let enabled_providers = self.repository.search(&enabled_query).await?;

        // 缓存启用的提供商
        for provider in &enabled_providers.hits.hits {
            self.cache_provider(&provider.source).await;
        }

        // 获取所有内置提供商
        let mut builtin_filters = HashMap::new();
        builtin_filters.insert("builtin".to_string(), serde_json::Value::Bool(true));

        let builtin_query = SearchQuery {
            q: None,
            size: Some(50),
            from: Some(0),
            sort: None,
            filters: Some(builtin_filters),
        };

        let builtin_providers = self.repository.search(&builtin_query).await?;

        // 缓存内置提供商
        for provider in &builtin_providers.hits.hits {
            self.cache_provider(&provider.source).await;
        }

        let duration = start_time.elapsed();
        let total_cached = enabled_providers.hits.hits.len() + builtin_providers.hits.hits.len();

        info!(
            "缓存预热完成: 缓存数量={}, 耗时={:?}",
            total_cached, duration
        );

        Ok(())
    }

    /// 清理过期缓存
    pub async fn cleanup_expired_cache(&self) {
        let start_time = Instant::now();

        // 清理查询缓存
        let mut query_cache = self.query_cache.write().await;
        let before_count = query_cache.len();

        let now = Instant::now();
        query_cache.retain(|_, (_, timestamp)| now.duration_since(*timestamp) < self.cache_ttl);

        let after_count = query_cache.len();
        let removed_count = before_count - after_count;

        drop(query_cache);

        // 清理性能指标中的过期数据
        self.metrics.cleanup_expired_data(24).await; // 保留24小时的数据

        let duration = start_time.elapsed();

        if removed_count > 0 {
            info!(
                "缓存清理完成: 清理查询缓存={}, 耗时={:?}",
                removed_count, duration
            );
        }
    }

    /// 获取性能统计信息
    pub async fn get_performance_stats(&self) -> HashMap<String, serde_json::Value> {
        let mut stats = HashMap::new();

        // 获取API性能统计
        if let Some(api_stats) = self
            .metrics
            .get_performance_stats(MetricType::ApiResponseTime, Some(3600))
            .await
        {
            stats.insert(
                "api_response_time".to_string(),
                serde_json::json!({
                    "avg_ms": api_stats.avg,
                    "p95_ms": api_stats.p95,
                    "p99_ms": api_stats.p99,
                    "count": api_stats.count
                }),
            );
        }

        // 获取数据库性能统计
        if let Some(db_stats) = self
            .metrics
            .get_performance_stats(MetricType::DatabaseQueryTime, Some(3600))
            .await
        {
            stats.insert(
                "database_query_time".to_string(),
                serde_json::json!({
                    "avg_ms": db_stats.avg,
                    "p95_ms": db_stats.p95,
                    "p99_ms": db_stats.p99,
                    "count": db_stats.count
                }),
            );
        }

        // 获取缓存命中率统计
        if let Some(cache_stats) = self
            .metrics
            .get_performance_stats(MetricType::CacheHitRate, Some(3600))
            .await
        {
            stats.insert(
                "cache_hit_rate".to_string(),
                serde_json::json!({
                    "avg_percent": cache_stats.avg,
                    "min_percent": cache_stats.min,
                    "max_percent": cache_stats.max,
                    "count": cache_stats.count
                }),
            );
        }

        // 获取当前并发请求数
        let concurrent_requests = self.metrics.get_concurrent_requests().await;
        stats.insert(
            "concurrent_requests".to_string(),
            serde_json::json!(concurrent_requests),
        );

        // 获取查询缓存统计
        let query_cache = self.query_cache.read().await;
        stats.insert(
            "query_cache_size".to_string(),
            serde_json::json!(query_cache.len()),
        );

        stats
    }

    /// 从缓存获取提供商
    async fn get_from_cache(&self, id: &str) -> Option<ModelProvider> {
        // TODO: 实现从缓存获取逻辑
        // 这里应该调用cache_service的方法
        None
    }

    /// 缓存提供商
    async fn cache_provider(&self, provider: &ModelProvider) {
        // TODO: 实现缓存提供商逻辑
        // 这里应该调用cache_service的方法
        debug!("缓存提供商: id={}, name={}", provider.id, provider.name);
    }

    /// 生成搜索缓存键
    fn generate_search_cache_key(&self, query: &SearchQuery) -> String {
        use std::{
            collections::hash_map::DefaultHasher,
            hash::{Hash, Hasher},
        };

        let mut hasher = DefaultHasher::new();
        query.hash(&mut hasher);
        format!("search:{:x}", hasher.finish())
    }

    /// 从缓存获取搜索结果
    async fn get_search_from_cache(&self, cache_key: &str) -> Option<SearchResponse> {
        let query_cache = self.query_cache.read().await;
        if let Some((result, timestamp)) = query_cache.get(cache_key) {
            let now = Instant::now();
            if now.duration_since(*timestamp) < self.cache_ttl {
                return Some(result.clone());
            }
        }
        None
    }

    /// 缓存搜索结果
    async fn cache_search_result(&self, cache_key: &str, result: &SearchResponse) {
        let mut query_cache = self.query_cache.write().await;
        query_cache.insert(cache_key.to_string(), (result.clone(), Instant::now()));

        // 限制缓存大小
        if query_cache.len() > 1000 {
            // 移除最旧的条目
            let oldest_key = query_cache
                .iter()
                .min_by_key(|(_, (_, timestamp))| timestamp)
                .map(|(key, _)| key.clone());

            if let Some(key) = oldest_key {
                query_cache.remove(&key);
            }
        }
    }
}
