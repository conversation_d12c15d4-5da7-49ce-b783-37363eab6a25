use std::sync::Arc;

use axum::{extract::State, http::StatusCode, response::J<PERSON>};
use serde::{Deserialize, Serialize};
use tracing::{error, info};

use crate::{
    app_state::AppState,
    services::{ReloadStats, ReloadStatus},
};

/// 配置重载请求
#[derive(Debug, Deserialize)]
pub struct ReloadConfigRequest {
    /// 是否强制重载（忽略验证错误）
    #[serde(default)]
    pub force: bool,
    /// 重载原因
    pub reason: Option<String>,
}

/// 配置重载响应
#[derive(Debug, Serialize)]
pub struct ReloadConfigResponse {
    /// 是否成功
    pub success: bool,
    /// 消息
    pub message: String,
    /// 重载状态
    pub status: ReloadStatus,
    /// 耗时（毫秒）
    pub duration_ms: Option<u64>,
}

/// 配置状态响应
#[derive(Debug, Serialize)]
pub struct ConfigStatusResponse {
    /// 当前状态
    pub status: ReloadStatus,
    /// 统计信息
    pub stats: ReloadStats,
    /// 是否启用自动重载
    pub auto_reload_enabled: bool,
    /// 配置文件路径
    pub config_path: String,
    /// 配置版本
    pub config_version: String,
}

/// 热重载控制请求
#[derive(Debug, Deserialize)]
pub struct HotReloadControlRequest {
    /// 是否启用
    pub enabled: bool,
}

/// 热重载控制响应
#[derive(Debug, Serialize)]
pub struct HotReloadControlResponse {
    /// 是否成功
    pub success: bool,
    /// 消息
    pub message: String,
    /// 当前状态
    pub enabled: bool,
}

/// 手动重载配置
/// POST /admin/config/reload
pub async fn reload_config(
    State(state): State<Arc<AppState>>,
    Json(request): Json<ReloadConfigRequest>,
) -> Result<Json<ReloadConfigResponse>, (StatusCode, Json<serde_json::Value>)> {
    info!(
        "收到配置重载请求，强制: {}, 原因: {:?}",
        request.force, request.reason
    );

    let start_time = std::time::Instant::now();

    // 获取配置重载服务
    let reload_service = match state.get_config_reload_service() {
        Some(service) => service,
        None => {
            error!("配置重载服务未初始化");
            return Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(serde_json::json!({
                    "error": "配置重载服务未初始化"
                })),
            ));
        }
    };

    // 执行重载
    let result = reload_service.reload_config().await;
    let duration = start_time.elapsed();

    match result {
        Ok(_) => {
            let response = ReloadConfigResponse {
                success: true,
                message: "配置重载成功".to_string(),
                status: reload_service.get_status(),
                duration_ms: Some(duration.as_millis() as u64),
            };
            info!("配置重载成功，耗时: {}ms", duration.as_millis());
            Ok(Json(response))
        }
        Err(e) => {
            let response = ReloadConfigResponse {
                success: false,
                message: format!("配置重载失败: {}", e),
                status: reload_service.get_status(),
                duration_ms: Some(duration.as_millis() as u64),
            };
            error!("配置重载失败: {}, 耗时: {}ms", e, duration.as_millis());

            // 如果不是强制重载，返回错误状态码
            if !request.force {
                return Err((
                    StatusCode::INTERNAL_SERVER_ERROR,
                    Json(serde_json::json!({
                        "error": response.message,
                        "status": response.status,
                        "duration_ms": response.duration_ms
                    })),
                ));
            }

            Ok(Json(response))
        }
    }
}

/// 获取配置状态
/// GET /admin/config/status
pub async fn get_config_status(
    State(state): State<Arc<AppState>>,
) -> Result<Json<ConfigStatusResponse>, (StatusCode, Json<serde_json::Value>)> {
    // 获取配置重载服务
    let reload_service = match state.get_config_reload_service() {
        Some(service) => service,
        None => {
            error!("配置重载服务未初始化");
            return Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(serde_json::json!({
                    "error": "配置重载服务未初始化"
                })),
            ));
        }
    };

    // 获取配置管理器
    let config_manager = state.get_config_manager();

    let response = ConfigStatusResponse {
        status: reload_service.get_status(),
        stats: reload_service.get_stats(),
        auto_reload_enabled: reload_service.is_auto_reload_enabled(),
        config_path: reload_service
            .get_config_path()
            .to_string_lossy()
            .to_string(),
        config_version: config_manager.get_config_version().to_string(),
    };

    Ok(Json(response))
}

/// 控制热重载功能
/// POST /admin/config/hot-reload
pub async fn control_hot_reload(
    State(state): State<Arc<AppState>>,
    Json(request): Json<HotReloadControlRequest>,
) -> Result<Json<HotReloadControlResponse>, (StatusCode, Json<serde_json::Value>)> {
    info!("收到热重载控制请求，启用: {}", request.enabled);

    // 获取配置重载服务
    let reload_service = match state.get_config_reload_service() {
        Some(service) => service,
        None => {
            error!("配置重载服务未初始化");
            return Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(serde_json::json!({
                    "error": "配置重载服务未初始化"
                })),
            ));
        }
    };

    let result = if request.enabled {
        reload_service.start_hot_reload().await
    } else {
        reload_service.stop_hot_reload();
        Ok(())
    };

    match result {
        Ok(_) => {
            let message = if request.enabled {
                "热重载已启用"
            } else {
                "热重载已禁用"
            };

            let response = HotReloadControlResponse {
                success: true,
                message: message.to_string(),
                enabled: reload_service.is_auto_reload_enabled(),
            };

            info!("{}", message);
            Ok(Json(response))
        }
        Err(e) => {
            let message = format!("热重载控制失败: {}", e);
            error!("{}", message);

            Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(serde_json::json!({
                    "error": message
                })),
            ))
        }
    }
}

/// 获取热重载状态
/// GET /admin/config/hot-reload
pub async fn get_hot_reload_status(
    State(state): State<Arc<AppState>>,
) -> Result<Json<HotReloadControlResponse>, (StatusCode, Json<serde_json::Value>)> {
    // 获取配置重载服务
    let reload_service = match state.get_config_reload_service() {
        Some(service) => service,
        None => {
            error!("配置重载服务未初始化");
            return Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(serde_json::json!({
                    "error": "配置重载服务未初始化"
                })),
            ));
        }
    };

    let enabled = reload_service.is_auto_reload_enabled();
    let response = HotReloadControlResponse {
        success: true,
        message: if enabled {
            "热重载已启用"
        } else {
            "热重载已禁用"
        }
        .to_string(),
        enabled,
    };

    Ok(Json(response))
}

/// 清理配置备份文件
/// DELETE /admin/config/backup
pub async fn cleanup_config_backup(
    State(state): State<Arc<AppState>>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<serde_json::Value>)> {
    info!("收到清理配置备份请求");

    // 获取配置重载服务
    let reload_service = match state.get_config_reload_service() {
        Some(service) => service,
        None => {
            error!("配置重载服务未初始化");
            return Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(serde_json::json!({
                    "error": "配置重载服务未初始化"
                })),
            ));
        }
    };

    match reload_service.cleanup_backup() {
        Ok(_) => {
            info!("配置备份文件清理成功");
            Ok(Json(serde_json::json!({
                "success": true,
                "message": "配置备份文件清理成功"
            })))
        }
        Err(e) => {
            error!("配置备份文件清理失败: {}", e);
            Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(serde_json::json!({
                    "error": format!("配置备份文件清理失败: {}", e)
                })),
            ))
        }
    }
}

/// 验证配置文件
/// POST /admin/config/validate
pub async fn validate_config(
    State(state): State<Arc<AppState>>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<serde_json::Value>)> {
    info!("收到配置验证请求");

    // 获取配置管理器
    let config_manager = state.get_config_manager();

    // 执行各种配置验证
    let mut validation_results = Vec::new();

    // 验证端口配置
    match config_manager.validate_port_config() {
        Ok(_) => validation_results.push(serde_json::json!({
            "type": "port_config",
            "status": "success",
            "message": "端口配置验证通过"
        })),
        Err(e) => validation_results.push(serde_json::json!({
            "type": "port_config",
            "status": "error",
            "message": format!("端口配置验证失败: {}", e)
        })),
    }

    // 验证数据库配置
    match config_manager.validate_database_config() {
        Ok(_) => validation_results.push(serde_json::json!({
            "type": "database_config",
            "status": "success",
            "message": "数据库配置验证通过"
        })),
        Err(e) => validation_results.push(serde_json::json!({
            "type": "database_config",
            "status": "error",
            "message": format!("数据库配置验证失败: {}", e)
        })),
    }

    // 验证TOML配置
    match config_manager.validate_toml_config() {
        Ok(_) => validation_results.push(serde_json::json!({
            "type": "toml_config",
            "status": "success",
            "message": "TOML配置验证通过"
        })),
        Err(e) => validation_results.push(serde_json::json!({
            "type": "toml_config",
            "status": "error",
            "message": format!("TOML配置验证失败: {}", e)
        })),
    }

    // 检查是否有验证失败
    let has_errors = validation_results
        .iter()
        .any(|result| result.get("status").and_then(|s| s.as_str()) == Some("error"));

    let response = serde_json::json!({
        "success": !has_errors,
        "message": if has_errors {
            "配置验证发现错误"
        } else {
            "所有配置验证通过"
        },
        "validations": validation_results
    });

    if has_errors {
        error!("配置验证发现错误");
        Err((StatusCode::BAD_REQUEST, Json(response)))
    } else {
        info!("所有配置验证通过");
        Ok(Json(response))
    }
}
