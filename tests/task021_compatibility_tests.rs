/// Go API兼容性测试 - TASK-021
///
/// 验证Rust版本API与Go版本的完全兼容性，包括请求/响应格式、错误码等

use axum::{
    body::Body,
    http::{Request, StatusCode},
    Router,
    routing::{get, post},
};
use serde_json::json;
use tower::ServiceExt;
use std::sync::Arc;

use coco_server::{
    config::config_manager::ConfigManager,
    handlers::{
        info_handler::{health_handler, info_handler},
        model_provider_handler::{
            create_model_provider_handler, get_model_provider_handler,
            search_model_provider_post_handler,
        },
    },
};

/// 创建测试路由器
fn create_test_router() -> Router {
    let config_manager = Arc::new(
        ConfigManager::new().expect("Failed to create config manager")
    );

    Router::new()
        .route("/_health", get(health_handler))
        .route("/_info", get(info_handler))
        .route("/model_provider/", post(create_model_provider_handler))
        .route("/model_provider/:id", get(get_model_provider_handler))
        .route("/model_provider/_search", post(search_model_provider_post_handler))
        .with_state(config_manager)
}

/// 测试健康检查端点兼容性
#[tokio::test]
async fn test_health_endpoint_compatibility() {
    let app = create_test_router();

    let request = Request::builder()
        .uri("/_health")
        .body(Body::empty())
        .unwrap();

    let response = app.oneshot(request).await.unwrap();

    // 验证状态码与Go版本兼容
    assert_eq!(response.status(), StatusCode::OK);

    // 验证响应体
    let body_bytes = axum::body::to_bytes(response.into_body(), usize::MAX).await.unwrap();
    let body_str = String::from_utf8(body_bytes.to_vec()).unwrap();

    // 健康检查应该返回简单的状态信息
    assert!(!body_str.is_empty(), "Health check should return status information");
}

/// 测试信息端点兼容性
#[tokio::test]
async fn test_info_endpoint_compatibility() {
    let app = create_test_router();

    let request = Request::builder()
        .uri("/_info")
        .body(Body::empty())
        .unwrap();

    let response = app.oneshot(request).await.unwrap();

    // 验证状态码与Go版本兼容
    assert_eq!(response.status(), StatusCode::OK);

    // 验证响应体
    let body_bytes = axum::body::to_bytes(response.into_body(), usize::MAX).await.unwrap();
    let body_str = String::from_utf8(body_bytes.to_vec()).unwrap();

    // 信息端点应该返回系统信息
    assert!(!body_str.is_empty(), "Info endpoint should return system information");

    // 尝试解析为JSON
    let json_result: Result<serde_json::Value, _> = serde_json::from_str(&body_str);
    assert!(json_result.is_ok(), "Info response should be valid JSON");
}

/// 测试API端点路径兼容性
#[tokio::test]
async fn test_api_endpoint_path_compatibility() {
    let app = create_test_router();

    // 测试模型提供商创建端点路径
    let request = Request::builder()
        .method("POST")
        .uri("/model_provider/")
        .header("content-type", "application/json")
        .body(Body::from("{}"))
        .unwrap();

    let response = app.oneshot(request).await.unwrap();

    // 路径应该存在（即使可能返回错误）
    assert_ne!(response.status(), StatusCode::NOT_FOUND);
}

/// 测试搜索端点路径兼容性
#[tokio::test]
async fn test_search_endpoint_path_compatibility() {
    let app = create_test_router();

    let search_query = json!({
        "query": {"match_all": {}},
        "size": 10
    });

    let request = Request::builder()
        .method("POST")
        .uri("/model_provider/_search")
        .header("content-type", "application/json")
        .body(Body::from(search_query.to_string()))
        .unwrap();

    let response = app.oneshot(request).await.unwrap();

    // 搜索端点路径应该存在
    assert_ne!(response.status(), StatusCode::NOT_FOUND);
}

/// 测试HTTP方法兼容性
#[tokio::test]
async fn test_http_methods_compatibility() {
    let app = create_test_router();

    // 测试GET方法
    let get_request = Request::builder()
        .method("GET")
        .uri("/model_provider/test-id")
        .body(Body::empty())
        .unwrap();

    let get_response = app.oneshot(get_request).await.unwrap();
    assert_ne!(get_response.status(), StatusCode::METHOD_NOT_ALLOWED);

    // 测试POST方法
    let app2 = create_test_router();
    let post_request = Request::builder()
        .method("POST")
        .uri("/model_provider/")
        .header("content-type", "application/json")
        .body(Body::from("{}"))
        .unwrap();

    let post_response = app2.oneshot(post_request).await.unwrap();
    assert_ne!(post_response.status(), StatusCode::METHOD_NOT_ALLOWED);
}
