/// 测试工具函数
use axum::{
    body::Body,
    http::{Request, StatusCode},
    Router,
};
use serde_json::Value;
use tower::ServiceExt;

/// HTTP测试客户端
pub struct TestClient {
    app: Router,
}

impl TestClient {
    pub fn new(app: Router) -> Self {
        Self { app }
    }

    /// 发送GET请求
    pub async fn get(&self, uri: &str) -> TestResponse {
        let request = Request::builder()
            .uri(uri)
            .body(Body::empty())
            .unwrap();

        let response = self.app.clone().oneshot(request).await.unwrap();
        TestResponse::new(response).await
    }

    /// 发送POST请求
    pub async fn post(&self, uri: &str, body: Value) -> TestResponse {
        let request = Request::builder()
            .uri(uri)
            .method("POST")
            .header("content-type", "application/json")
            .body(Body::from(body.to_string()))
            .unwrap();

        let response = self.app.clone().oneshot(request).await.unwrap();
        TestResponse::new(response).await
    }

    /// 发送PUT请求
    pub async fn put(&self, uri: &str, body: Value) -> TestResponse {
        let request = Request::builder()
            .uri(uri)
            .method("PUT")
            .header("content-type", "application/json")
            .body(Body::from(body.to_string()))
            .unwrap();

        let response = self.app.clone().oneshot(request).await.unwrap();
        TestResponse::new(response).await
    }

    /// 发送DELETE请求
    pub async fn delete(&self, uri: &str) -> TestResponse {
        let request = Request::builder()
            .uri(uri)
            .method("DELETE")
            .body(Body::empty())
            .unwrap();

        let response = self.app.clone().oneshot(request).await.unwrap();
        TestResponse::new(response).await
    }

    /// 发送OPTIONS请求
    pub async fn options(&self, uri: &str) -> TestResponse {
        let request = Request::builder()
            .uri(uri)
            .method("OPTIONS")
            .body(Body::empty())
            .unwrap();

        let response = self.app.clone().oneshot(request).await.unwrap();
        TestResponse::new(response).await
    }

    /// 发送带认证头的请求
    pub async fn get_with_auth(&self, uri: &str, token: &str) -> TestResponse {
        let request = Request::builder()
            .uri(uri)
            .header("Authorization", format!("Bearer {}", token))
            .body(Body::empty())
            .unwrap();

        let response = self.app.clone().oneshot(request).await.unwrap();
        TestResponse::new(response).await
    }

    /// 发送带认证头的POST请求
    pub async fn post_with_auth(&self, uri: &str, body: Value, token: &str) -> TestResponse {
        let request = Request::builder()
            .uri(uri)
            .method("POST")
            .header("content-type", "application/json")
            .header("Authorization", format!("Bearer {}", token))
            .body(Body::from(body.to_string()))
            .unwrap();

        let response = self.app.clone().oneshot(request).await.unwrap();
        TestResponse::new(response).await
    }
}

/// 测试响应包装器
pub struct TestResponse {
    pub status: StatusCode,
    pub body: String,
    pub json: Option<Value>,
}

impl TestResponse {
    async fn new(response: axum::response::Response) -> Self {
        let status = response.status();
        let body_bytes = axum::body::to_bytes(response.into_body(), usize::MAX)
            .await
            .unwrap();
        let body = String::from_utf8(body_bytes.to_vec()).unwrap();
        
        let json = if !body.is_empty() {
            serde_json::from_str(&body).ok()
        } else {
            None
        };

        Self { status, body, json }
    }

    /// 断言状态码
    pub fn assert_status(&self, expected: StatusCode) -> &Self {
        assert_eq!(
            self.status, expected,
            "Expected status {}, got {}. Body: {}",
            expected, self.status, self.body
        );
        self
    }

    /// 断言JSON字段
    pub fn assert_json_field(&self, field: &str, expected: Value) -> &Self {
        let json = self.json.as_ref().expect("Response is not JSON");
        let actual = json.get(field).expect(&format!("Field '{}' not found", field));
        assert_eq!(
            actual, &expected,
            "Field '{}' mismatch. Expected: {}, Actual: {}",
            field, expected, actual
        );
        self
    }

    /// 断言JSON包含字段
    pub fn assert_json_contains(&self, field: &str) -> &Self {
        let json = self.json.as_ref().expect("Response is not JSON");
        assert!(
            json.get(field).is_some(),
            "Field '{}' not found in response: {}",
            field, json
        );
        self
    }

    /// 获取JSON值
    pub fn json(&self) -> &Value {
        self.json.as_ref().expect("Response is not JSON")
    }

    /// 获取JSON字段值
    pub fn json_field(&self, field: &str) -> &Value {
        self.json().get(field).expect(&format!("Field '{}' not found", field))
    }
}

/// 等待条件满足的辅助函数
pub async fn wait_for_condition<F, Fut>(mut condition: F, timeout_secs: u64) -> bool
where
    F: FnMut() -> Fut,
    Fut: std::future::Future<Output = bool>,
{
    let start = std::time::Instant::now();
    let timeout = std::time::Duration::from_secs(timeout_secs);

    while start.elapsed() < timeout {
        if condition().await {
            return true;
        }
        tokio::time::sleep(std::time::Duration::from_millis(100)).await;
    }

    false
}

/// 生成随机字符串
pub fn random_string(length: usize) -> String {
    use rand::Rng;
    const CHARSET: &[u8] = b"abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    let mut rng = rand::thread_rng();
    
    (0..length)
        .map(|_| {
            let idx = rng.gen_range(0..CHARSET.len());
            CHARSET[idx] as char
        })
        .collect()
}

/// 生成测试用的JWT令牌
pub fn generate_test_jwt() -> String {
    use jsonwebtoken::{encode, EncodingKey, Header};
    use serde::{Deserialize, Serialize};
    use std::time::{SystemTime, UNIX_EPOCH};

    #[derive(Debug, Serialize, Deserialize)]
    struct Claims {
        sub: String,
        exp: usize,
    }

    let claims = Claims {
        sub: "test-user".to_string(),
        exp: (SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs() + 3600) as usize,
    };

    encode(
        &Header::default(),
        &claims,
        &EncodingKey::from_secret("test-secret-key-for-testing-only".as_ref()),
    )
    .unwrap()
}
