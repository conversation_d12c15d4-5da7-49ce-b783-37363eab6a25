/// 自定义断言宏和函数
use serde_json::Value;

/// 断言JSON响应格式符合Go版本兼容性
pub fn assert_go_compatible_response(response: &Value, expected_type: &str) {
    match expected_type {
        "create" => {
            assert!(response.get("_id").is_some(), "Create response missing _id field");
            assert!(response.get("result").is_some(), "Create response missing result field");
            assert_eq!(
                response.get("result").unwrap().as_str().unwrap(),
                "created",
                "Create response result should be 'created'"
            );
        }
        "get" => {
            assert!(response.get("_id").is_some(), "Get response missing _id field");
            assert!(response.get("found").is_some(), "Get response missing found field");
            assert!(response.get("_source").is_some(), "Get response missing _source field");
            assert_eq!(
                response.get("found").unwrap().as_bool().unwrap(),
                true,
                "Get response found should be true"
            );
        }
        "update" => {
            assert!(response.get("_id").is_some(), "Update response missing _id field");
            assert!(response.get("result").is_some(), "Update response missing result field");
            assert_eq!(
                response.get("result").unwrap().as_str().unwrap(),
                "updated",
                "Update response result should be 'updated'"
            );
        }
        "delete" => {
            assert!(response.get("_id").is_some(), "Delete response missing _id field");
            assert!(response.get("result").is_some(), "Delete response missing result field");
            assert_eq!(
                response.get("result").unwrap().as_str().unwrap(),
                "deleted",
                "Delete response result should be 'deleted'"
            );
        }
        "search" => {
            assert!(response.get("hits").is_some(), "Search response missing hits field");
            let hits = response.get("hits").unwrap();
            assert!(hits.get("total").is_some(), "Search hits missing total field");
            assert!(hits.get("hits").is_some(), "Search hits missing hits array");
        }
        _ => panic!("Unknown response type: {}", expected_type),
    }
}

/// 断言敏感字段已被过滤
pub fn assert_sensitive_fields_filtered(response: &Value) {
    // 检查api_key字段是否被过滤或为空
    if let Some(api_key) = response.get("api_key") {
        assert!(
            api_key.is_null() || api_key.as_str().unwrap_or("").is_empty(),
            "API key should be filtered from response"
        );
    }

    // 检查_source中的敏感字段
    if let Some(source) = response.get("_source") {
        if let Some(api_key) = source.get("api_key") {
            assert!(
                api_key.is_null() || api_key.as_str().unwrap_or("").is_empty(),
                "API key should be filtered from _source"
            );
        }
    }
}

/// 断言内置提供商保护逻辑
pub fn assert_builtin_protection_error(response: &Value) {
    assert!(response.get("error").is_some(), "Error response missing error field");
    let error_msg = response.get("error").unwrap().as_str().unwrap();
    assert!(
        error_msg.contains("内置") || error_msg.contains("builtin") || error_msg.contains("protected"),
        "Error message should indicate builtin provider protection: {}",
        error_msg
    );
}

/// 断言验证错误响应
pub fn assert_validation_error(response: &Value, field: Option<&str>) {
    assert!(response.get("error").is_some(), "Validation error response missing error field");
    
    if let Some(field_name) = field {
        let error_msg = response.get("error").unwrap().as_str().unwrap();
        assert!(
            error_msg.contains(field_name),
            "Validation error should mention field '{}': {}",
            field_name,
            error_msg
        );
    }
}

/// 断言分页响应格式
pub fn assert_pagination_response(response: &Value, expected_size: usize, expected_from: usize) {
    let hits = response.get("hits").expect("Response missing hits field");
    
    // 检查分页参数
    if let Some(size) = hits.get("size") {
        assert_eq!(
            size.as_u64().unwrap() as usize,
            expected_size,
            "Page size mismatch"
        );
    }
    
    if let Some(from) = hits.get("from") {
        assert_eq!(
            from.as_u64().unwrap() as usize,
            expected_from,
            "Page from mismatch"
        );
    }
    
    // 检查结果数组
    let hits_array = hits.get("hits").expect("Hits missing hits array");
    assert!(hits_array.is_array(), "Hits should be an array");
}

/// 断言搜索结果排序
pub fn assert_search_results_sorted(response: &Value, sort_field: &str, ascending: bool) {
    let hits = response.get("hits").expect("Response missing hits field");
    let hits_array = hits.get("hits").expect("Hits missing hits array").as_array().unwrap();
    
    if hits_array.len() < 2 {
        return; // 不足两个结果，无法验证排序
    }
    
    for i in 0..hits_array.len() - 1 {
        let current = &hits_array[i];
        let next = &hits_array[i + 1];
        
        let current_value = current.get("_source")
            .and_then(|s| s.get(sort_field))
            .expect(&format!("Sort field '{}' not found in current item", sort_field));
        
        let next_value = next.get("_source")
            .and_then(|s| s.get(sort_field))
            .expect(&format!("Sort field '{}' not found in next item", sort_field));
        
        // 简单的字符串比较（实际实现可能需要更复杂的比较逻辑）
        let current_str = current_value.as_str().unwrap_or("");
        let next_str = next_value.as_str().unwrap_or("");
        
        if ascending {
            assert!(
                current_str <= next_str,
                "Results not sorted in ascending order by '{}': '{}' > '{}'",
                sort_field, current_str, next_str
            );
        } else {
            assert!(
                current_str >= next_str,
                "Results not sorted in descending order by '{}': '{}' < '{}'",
                sort_field, current_str, next_str
            );
        }
    }
}

/// 断言错误响应格式
pub fn assert_error_response_format(response: &Value, expected_status: u16) {
    assert!(response.get("error").is_some(), "Error response missing error field");
    
    if let Some(status) = response.get("status") {
        assert_eq!(
            status.as_u64().unwrap() as u16,
            expected_status,
            "Error status code mismatch"
        );
    }
}

/// 断言CORS头部
pub fn assert_cors_headers(headers: &axum::http::HeaderMap) {
    assert!(
        headers.contains_key("access-control-allow-origin"),
        "CORS headers missing Access-Control-Allow-Origin"
    );
    assert!(
        headers.contains_key("access-control-allow-methods"),
        "CORS headers missing Access-Control-Allow-Methods"
    );
}

/// 断言响应时间在合理范围内
pub fn assert_response_time(duration: std::time::Duration, max_ms: u64) {
    assert!(
        duration.as_millis() <= max_ms as u128,
        "Response time {}ms exceeds maximum {}ms",
        duration.as_millis(),
        max_ms
    );
}

/// 断言数据库记录存在
pub async fn assert_record_exists(table: &str, id: &str) -> bool {
    use coco_server::database::DB;
    
    let result: Vec<surrealdb::sql::Value> = DB
        .query(&format!("SELECT * FROM {}:{}", table, id))
        .await
        .unwrap_or_default();
    
    !result.is_empty()
}

/// 断言数据库记录不存在
pub async fn assert_record_not_exists(table: &str, id: &str) -> bool {
    !assert_record_exists(table, id).await
}
