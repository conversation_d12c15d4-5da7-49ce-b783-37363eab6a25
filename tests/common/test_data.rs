/// 测试数据生成和管理
use chrono::Utc;
use coco_server::models::model_provider::{ModelProvider, ModelConfig, ModelSettings};
use serde_json::json;
use uuid::Uuid;

/// 创建测试用的模型提供商
pub fn create_test_model_provider(name: &str, builtin: bool) -> ModelProvider {
    ModelProvider {
        id: Uuid::new_v4().to_string(),
        name: name.to_string(),
        description: format!("测试提供商: {}", name),
        api_type: "openai".to_string(),
        api_url: "https://api.openai.com/v1".to_string(),
        api_key: if builtin { "".to_string() } else { "test-api-key".to_string() },
        enabled: true,
        builtin,
        created: Utc::now(),
        updated: Utc::now(),
        config: ModelConfig {
            max_tokens: Some(4096),
            temperature: Some(0.7),
            top_p: Some(1.0),
            frequency_penalty: Some(0.0),
            presence_penalty: Some(0.0),
            timeout: Some(30),
            retry_count: Some(3),
            custom_headers: None,
        },
        settings: ModelSettings {
            rate_limit: Some(100),
            concurrent_requests: Some(10),
            priority: Some(1),
            fallback_providers: None,
            health_check_url: None,
            health_check_interval: Some(300),
        },
        models: vec![
            json!({
                "id": "gpt-3.5-turbo",
                "name": "GPT-3.5 Turbo",
                "description": "Fast and efficient model",
                "context_length": 4096,
                "pricing": {
                    "input": 0.0015,
                    "output": 0.002
                }
            }),
            json!({
                "id": "gpt-4",
                "name": "GPT-4",
                "description": "Most capable model",
                "context_length": 8192,
                "pricing": {
                    "input": 0.03,
                    "output": 0.06
                }
            })
        ],
        tags: vec!["test".to_string(), "openai".to_string()],
        metadata: json!({
            "test": true,
            "version": "1.0",
            "created_by": "test_suite"
        }),
    }
}

/// 创建内置测试提供商
pub fn create_builtin_test_provider() -> ModelProvider {
    create_test_model_provider("Test Builtin Provider", true)
}

/// 创建自定义测试提供商
pub fn create_custom_test_provider() -> ModelProvider {
    create_test_model_provider("Test Custom Provider", false)
}

/// 创建多个测试提供商
pub fn create_multiple_test_providers(count: usize) -> Vec<ModelProvider> {
    (0..count)
        .map(|i| create_test_model_provider(&format!("Test Provider {}", i + 1), i % 2 == 0))
        .collect()
}

/// 创建用于搜索测试的提供商
pub fn create_search_test_providers() -> Vec<ModelProvider> {
    vec![
        ModelProvider {
            name: "OpenAI Provider".to_string(),
            description: "Official OpenAI provider".to_string(),
            api_type: "openai".to_string(),
            enabled: true,
            builtin: true,
            tags: vec!["openai".to_string(), "official".to_string()],
            ..create_test_model_provider("OpenAI Provider", true)
        },
        ModelProvider {
            name: "Anthropic Provider".to_string(),
            description: "Anthropic Claude provider".to_string(),
            api_type: "anthropic".to_string(),
            enabled: true,
            builtin: false,
            tags: vec!["anthropic".to_string(), "claude".to_string()],
            ..create_test_model_provider("Anthropic Provider", false)
        },
        ModelProvider {
            name: "Local Provider".to_string(),
            description: "Local model provider".to_string(),
            api_type: "local".to_string(),
            enabled: false,
            builtin: false,
            tags: vec!["local".to_string(), "custom".to_string()],
            ..create_test_model_provider("Local Provider", false)
        },
    ]
}

/// 创建无效的测试数据
pub fn create_invalid_test_provider() -> serde_json::Value {
    json!({
        "name": "", // 空名称
        "description": "Invalid provider",
        "api_type": "invalid_type",
        "api_url": "not-a-url",
        "enabled": "not-a-boolean", // 错误的类型
        "config": {
            "max_tokens": "not-a-number" // 错误的类型
        }
    })
}

/// 创建用于更新测试的数据
pub fn create_update_test_data() -> serde_json::Value {
    json!({
        "description": "Updated description",
        "enabled": false,
        "config": {
            "max_tokens": 2048,
            "temperature": 0.5
        },
        "tags": ["updated", "test"]
    })
}

/// 创建搜索查询参数
pub fn create_search_query(query: &str, filters: Option<serde_json::Value>) -> serde_json::Value {
    let mut search_body = json!({
        "query": {
            "match": {
                "_all": query
            }
        },
        "size": 10,
        "from": 0
    });

    if let Some(filters) = filters {
        search_body["query"] = json!({
            "bool": {
                "must": [
                    search_body["query"]
                ],
                "filter": filters
            }
        });
    }

    search_body
}

/// 创建分页查询参数
pub fn create_pagination_query(size: usize, from: usize) -> serde_json::Value {
    json!({
        "size": size,
        "from": from,
        "sort": [
            {
                "created": {
                    "order": "desc"
                }
            }
        ]
    })
}
