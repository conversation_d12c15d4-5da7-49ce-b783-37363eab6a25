/// 测试应用程序设置和配置
use std::sync::Arc;

use axum::{
    routing::{delete, get, post, put},
    Router,
};
use coco_server::{
    app_state::AppState,
    auth::{jwt_cache::JwtCache, token_blacklist::TokenBlacklist},
    config::{app_config::AppConfig, config_manager::ConfigManager},
    database::{init_database, SurrealDBClient},
    handlers::{
        info_handler::{health_handler, info_handler},
        model_provider_handler::{
            create_model_provider_handler, delete_model_provider_handler,
            get_model_provider_handler, search_model_provider_get_handler,
            search_model_provider_options_handler, search_model_provider_post_handler,
            update_model_provider_handler,
        },
        setup_handler::initialize_handler,
    },
    middleware::auth_middleware::auth_middleware,
    repositories::token_repository::TokenRepository,
    services::{config_reload_service::ConfigReloadService, token_service::TokenService},
};
use tower_http::cors::CorsLayer;

/// 测试应用程序构建器
pub struct TestAppBuilder {
    config: Option<AppConfig>,
    with_auth: bool,
    with_database: bool,
}

impl TestAppBuilder {
    pub fn new() -> Self {
        Self {
            config: None,
            with_auth: false,
            with_database: false,
        }
    }

    pub fn with_config(mut self, config: AppConfig) -> Self {
        self.config = Some(config);
        self
    }

    pub fn with_auth(mut self) -> Self {
        self.with_auth = true;
        self
    }

    pub fn with_database(mut self) -> Self {
        self.with_database = true;
        self
    }

    pub async fn build(self) -> TestApp {
        let config = self.config.unwrap_or_else(|| create_test_config());
        
        // 创建配置管理器
        let config_manager = Arc::new(ConfigManager::new(config.clone()));
        
        // 创建数据库客户端（如果需要）
        let db_client = if self.with_database {
            if let Err(e) = init_database(&config.database).await {
                tracing::warn!("测试数据库初始化失败: {}", e);
            }
            Some(Arc::new(SurrealDBClient::new()))
        } else {
            None
        };

        // 创建应用状态
        let token_repository = Arc::new(TokenRepository::new());
        let token_service = Arc::new(TokenService::new(
            token_repository.clone(),
            config.jwt.secret.clone(),
            config.jwt.expiration,
        ));
        let token_blacklist = Arc::new(TokenBlacklist::new());
        let jwt_cache = Arc::new(JwtCache::new());
        let config_reload_service = Some(Arc::new(ConfigReloadService::new(
            config_manager.clone(),
        )));

        let app_state = AppState {
            config_manager,
            db_client,
            token_repository,
            token_service,
            token_blacklist,
            jwt_cache,
            config_reload_service,
        };

        // 构建路由
        let app = create_test_router(app_state, self.with_auth).await;

        TestApp { app, config }
    }
}

/// 测试应用程序
pub struct TestApp {
    pub app: Router,
    pub config: AppConfig,
}

impl TestApp {
    pub fn builder() -> TestAppBuilder {
        TestAppBuilder::new()
    }
}

/// 创建测试配置
pub fn create_test_config() -> AppConfig {
    AppConfig {
        server: coco_server::config::app_config::ServerConfig {
            host: "127.0.0.1".to_string(),
            port: 0, // 使用随机端口
            tls: None,
        },
        database: coco_server::config::app_config::DatabaseConfig {
            url: "memory".to_string(), // 使用内存数据库
            username: "root".to_string(),
            password: "root".to_string(),
            namespace: "test".to_string(),
            database: "test".to_string(),
        },
        jwt: coco_server::config::app_config::JwtConfig {
            secret: "test-secret-key-for-testing-only".to_string(),
            expiration: 3600,
        },
        cache: coco_server::config::app_config::CacheConfig {
            ttl: 300,
            max_size: 1000,
        },
        log: coco_server::config::app_config::LogConfig {
            level: "debug".to_string(),
            format: "json".to_string(),
        },
    }
}

/// 创建测试路由
async fn create_test_router(app_state: AppState, with_auth: bool) -> Router {
    let mut router = Router::new()
        // 公开端点
        .route("/_info", get(info_handler))
        .route("/_health", get(health_handler))
        .route("/setup/_initialize", post(initialize_handler));

    // 受保护的端点
    let protected_routes = Router::new()
        .route("/model_provider/", post(create_model_provider_handler))
        .route("/model_provider/:id", get(get_model_provider_handler))
        .route("/model_provider/:id", put(update_model_provider_handler))
        .route("/model_provider/:id", delete(delete_model_provider_handler))
        .route("/model_provider/_search", get(search_model_provider_get_handler))
        .route("/model_provider/_search", post(search_model_provider_post_handler))
        .route("/model_provider/_search", options(search_model_provider_options_handler));

    if with_auth {
        router = router.merge(protected_routes.layer(axum::middleware::from_fn_with_state(
            app_state.clone(),
            auth_middleware,
        )));
    } else {
        router = router.merge(protected_routes);
    }

    router
        .layer(CorsLayer::permissive())
        .with_state(app_state)
}
