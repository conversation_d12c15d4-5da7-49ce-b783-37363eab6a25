/// API集成测试 - TASK-021
///
/// 测试所有API端点的功能正确性，包括CRUD操作、搜索功能、错误处理等

use axum::{
    body::Body,
    http::{Request, StatusCode},
    Router,
    routing::{get, post},
};
use serde_json::json;
use tower::ServiceExt;
use std::sync::Arc;

use coco_server::{
    config::config_manager::ConfigManager,
    handlers::{
        info_handler::{health_handler, info_handler},
        model_provider_handler::{
            create_model_provider_handler, get_model_provider_handler,
            search_model_provider_post_handler,
        },
        setup_handler::initialize_handler,
    },
};

/// 创建测试路由器
fn create_test_router() -> Router {
    let config_manager = Arc::new(
        ConfigManager::new().expect("Failed to create config manager")
    );

    Router::new()
        .route("/_health", get(health_handler))
        .route("/_info", get(info_handler))
        .route("/setup/_initialize", post(initialize_handler))
        .route("/model_provider/", post(create_model_provider_handler))
        .route("/model_provider/:id", get(get_model_provider_handler))
        .route("/model_provider/_search", post(search_model_provider_post_handler))
        .with_state(config_manager)
}

/// 测试健康检查端点
#[tokio::test]
async fn test_health_endpoint() {
    let app = create_test_router();

    let request = Request::builder()
        .uri("/_health")
        .body(Body::empty())
        .unwrap();

    let response = app.oneshot(request).await.unwrap();
    assert_eq!(response.status(), StatusCode::OK);
}

/// 测试信息端点
#[tokio::test]
async fn test_info_endpoint() {
    let app = create_test_router();

    let request = Request::builder()
        .uri("/_info")
        .body(Body::empty())
        .unwrap();

    let response = app.oneshot(request).await.unwrap();
    assert_eq!(response.status(), StatusCode::OK);
}

/// 测试初始化端点
#[tokio::test]
async fn test_initialize_endpoint() {
    let app = create_test_router();

    let request = Request::builder()
        .uri("/setup/_initialize")
        .method("POST")
        .body(Body::empty())
        .unwrap();

    let response = app.oneshot(request).await.unwrap();
    assert_eq!(response.status(), StatusCode::OK);
}

/// 测试模型提供商创建端点路由存在
#[tokio::test]
async fn test_create_model_provider_route_exists() {
    let app = create_test_router();

    let request_body = json!({
        "name": "Test Provider",
        "api_key": "test-key",
        "api_type": "openai",
        "api_url": "https://api.openai.com/v1",
        "enabled": true,
        "description": "Test description"
    });

    let request = Request::builder()
        .method("POST")
        .uri("/model_provider/")
        .header("content-type", "application/json")
        .body(Body::from(request_body.to_string()))
        .unwrap();

    let response = app.oneshot(request).await.unwrap();

    // 路由应该存在并能处理请求（可能返回错误因为数据库未初始化）
    assert!(
        response.status() == StatusCode::INTERNAL_SERVER_ERROR
            || response.status() == StatusCode::BAD_REQUEST
            || response.status() == StatusCode::CREATED
    );
}

/// 测试获取模型提供商端点路由存在
#[tokio::test]
async fn test_get_model_provider_route_exists() {
    let app = create_test_router();

    let request = Request::builder()
        .method("GET")
        .uri("/model_provider/test-id")
        .body(Body::empty())
        .unwrap();

    let response = app.oneshot(request).await.unwrap();

    // 路由应该存在并能处理请求
    assert!(
        response.status() == StatusCode::INTERNAL_SERVER_ERROR
            || response.status() == StatusCode::NOT_FOUND
    );
}

/// 测试搜索端点路由存在
#[tokio::test]
async fn test_search_providers_route_exists() {
    let app = create_test_router();

    let search_query = json!({
        "query": {
            "match_all": {}
        },
        "size": 10
    });

    let request = Request::builder()
        .method("POST")
        .uri("/model_provider/_search")
        .header("content-type", "application/json")
        .body(Body::from(search_query.to_string()))
        .unwrap();

    let response = app.oneshot(request).await.unwrap();

    // 路由应该存在并能处理请求
    assert!(
        response.status() == StatusCode::INTERNAL_SERVER_ERROR
            || response.status() == StatusCode::OK
    );
}

/// 测试无效请求处理
#[tokio::test]
async fn test_invalid_request_handling() {
    let app = create_test_router();

    // 测试无效的JSON
    let request = Request::builder()
        .method("POST")
        .uri("/model_provider/")
        .header("content-type", "application/json")
        .body(Body::from("invalid json"))
        .unwrap();

    let response = app.oneshot(request).await.unwrap();

    // 应该返回400错误而不是500错误
    assert!(
        response.status() == StatusCode::BAD_REQUEST
            || response.status() == StatusCode::UNPROCESSABLE_ENTITY
    );
}

/// 测试不存在的路由
#[tokio::test]
async fn test_nonexistent_route() {
    let app = create_test_router();

    let request = Request::builder()
        .uri("/nonexistent/route")
        .body(Body::empty())
        .unwrap();

    let response = app.oneshot(request).await.unwrap();
    assert_eq!(response.status(), StatusCode::NOT_FOUND);
}
