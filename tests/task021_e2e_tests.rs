/// 端到端用户场景测试 - TASK-021
///
/// 测试完整的用户使用场景，从系统启动到完整的业务流程

use axum::{
    body::Body,
    http::{Request, StatusCode},
    Router,
    routing::{get, post},
};
use serde_json::json;
use tower::ServiceExt;
use std::sync::Arc;

use coco_server::{
    config::config_manager::ConfigManager,
    handlers::{
        info_handler::{health_handler, info_handler},
        model_provider_handler::{
            create_model_provider_handler, get_model_provider_handler,
            search_model_provider_post_handler,
        },
        setup_handler::initialize_handler,
    },
};

/// 创建测试路由器
fn create_test_router() -> Router {
    let config_manager = Arc::new(
        ConfigManager::new().expect("Failed to create config manager")
    );

    Router::new()
        .route("/_health", get(health_handler))
        .route("/_info", get(info_handler))
        .route("/setup/_initialize", post(initialize_handler))
        .route("/model_provider/", post(create_model_provider_handler))
        .route("/model_provider/:id", get(get_model_provider_handler))
        .route("/model_provider/_search", post(search_model_provider_post_handler))
        .with_state(config_manager)
}

/// 场景1: 系统启动和基础功能检查
#[tokio::test]
async fn test_system_startup_scenario() {
    let app = create_test_router();

    println!("🧪 测试场景：系统启动和基础功能检查");

    // 步骤1: 检查健康状态
    println!("  1. 检查系统健康状态...");
    let health_request = Request::builder()
        .uri("/_health")
        .body(Body::empty())
        .unwrap();

    let health_response = app.oneshot(health_request).await.unwrap();
    assert_eq!(health_response.status(), StatusCode::OK);

    // 步骤2: 检查系统信息
    println!("  2. 获取系统信息...");
    let app2 = create_test_router();
    let info_request = Request::builder()
        .uri("/_info")
        .body(Body::empty())
        .unwrap();

    let info_response = app2.oneshot(info_request).await.unwrap();
    assert_eq!(info_response.status(), StatusCode::OK);

    // 步骤3: 初始化系统
    println!("  3. 初始化系统...");
    let app3 = create_test_router();
    let init_request = Request::builder()
        .uri("/setup/_initialize")
        .method("POST")
        .body(Body::empty())
        .unwrap();

    let init_response = app3.oneshot(init_request).await.unwrap();
    assert_eq!(init_response.status(), StatusCode::OK);

    println!("✅ 系统启动场景测试完成");
}

/// 场景2: API端点完整性测试
#[tokio::test]
async fn test_api_endpoints_completeness_scenario() {
    let app = create_test_router();

    println!("🧪 测试场景：API端点完整性");

    // 步骤1: 测试模型提供商创建端点
    println!("  1. 测试创建端点...");
    let create_request = Request::builder()
        .method("POST")
        .uri("/model_provider/")
        .header("content-type", "application/json")
        .body(Body::from(json!({
            "name": "Test Provider",
            "api_type": "openai",
            "api_url": "https://api.openai.com/v1",
            "enabled": true
        }).to_string()))
        .unwrap();

    let create_response = app.oneshot(create_request).await.unwrap();
    // 端点应该存在（可能返回错误因为数据库未初始化）
    assert_ne!(create_response.status(), StatusCode::NOT_FOUND);

    // 步骤2: 测试获取端点
    println!("  2. 测试获取端点...");
    let app2 = create_test_router();
    let get_request = Request::builder()
        .method("GET")
        .uri("/model_provider/test-id")
        .body(Body::empty())
        .unwrap();

    let get_response = app2.oneshot(get_request).await.unwrap();
    assert_ne!(get_response.status(), StatusCode::NOT_FOUND);

    // 步骤3: 测试搜索端点
    println!("  3. 测试搜索端点...");
    let app3 = create_test_router();
    let search_request = Request::builder()
        .method("POST")
        .uri("/model_provider/_search")
        .header("content-type", "application/json")
        .body(Body::from(json!({
            "query": {"match_all": {}},
            "size": 10
        }).to_string()))
        .unwrap();

    let search_response = app3.oneshot(search_request).await.unwrap();
    assert_ne!(search_response.status(), StatusCode::NOT_FOUND);

    println!("✅ API端点完整性测试完成");
}

/// 场景3: 错误处理和容错测试
#[tokio::test]
async fn test_error_handling_scenario() {
    let app = create_test_router();

    println!("🧪 测试场景：错误处理和容错");

    // 步骤1: 测试无效JSON处理
    println!("  1. 测试无效JSON处理...");
    let invalid_json_request = Request::builder()
        .method("POST")
        .uri("/model_provider/")
        .header("content-type", "application/json")
        .body(Body::from("invalid json"))
        .unwrap();

    let invalid_response = app.oneshot(invalid_json_request).await.unwrap();
    assert!(
        invalid_response.status().as_u16() < 500,
        "Invalid JSON should not cause server error"
    );

    // 步骤2: 测试不存在的路由
    println!("  2. 测试不存在的路由...");
    let app2 = create_test_router();
    let not_found_request = Request::builder()
        .uri("/nonexistent/route")
        .body(Body::empty())
        .unwrap();

    let not_found_response = app2.oneshot(not_found_request).await.unwrap();
    assert_eq!(not_found_response.status(), StatusCode::NOT_FOUND);

    // 步骤3: 测试多个并发请求
    println!("  3. 测试并发请求处理...");
    let mut handles = vec![];

    for i in 0..3 {
        let app_clone = create_test_router();
        let handle = tokio::spawn(async move {
            let request = Request::builder()
                .uri("/_health")
                .body(Body::empty())
                .unwrap();

            let response = app_clone.oneshot(request).await.unwrap();
            (i, response.status())
        });
        handles.push(handle);
    }

    // 等待所有请求完成
    for handle in handles {
        let (i, status) = handle.await.unwrap();
        assert_eq!(status, StatusCode::OK, "Concurrent request {} should succeed", i);
    }

    println!("✅ 错误处理和容错测试完成");
}
