/// 端到端用户场景测试
/// 
/// 测试完整的用户使用场景，从系统启动到完整的业务流程

use axum::http::StatusCode;
use serde_json::json;
use std::time::Duration;
use tokio::time::sleep;

mod common;
use common::{
    assertions::*,
    test_app::TestApp,
    test_data::*,
    test_database::*,
    test_utils::*,
};

/// 场景1: 新用户配置提供商的完整流程
#[tokio::test]
async fn test_new_user_setup_provider_scenario() {
    let test_db = setup_test_database().await;
    let app = TestApp::builder()
        .with_database()
        .build()
        .await;
    let client = TestClient::new(app.app);

    println!("🧪 测试场景：新用户配置提供商");

    // 步骤1: 系统启动，检查健康状态
    println!("  1. 检查系统健康状态...");
    let health_response = client.get("/_health").await;
    health_response.assert_status(StatusCode::OK);

    // 步骤2: 检查系统信息
    println!("  2. 获取系统信息...");
    let info_response = client.get("/_info").await;
    info_response.assert_status(StatusCode::OK);

    // 步骤3: 用户查看现有提供商（应该为空或只有内置提供商）
    println!("  3. 查看现有提供商...");
    let search_response = client.post("/model_provider/_search", json!({
        "query": {"match_all": {}},
        "size": 100
    })).await;
    search_response.assert_status(StatusCode::OK);

    // 步骤4: 用户创建自定义提供商
    println!("  4. 创建自定义提供商...");
    let custom_provider = json!({
        "name": "My Custom OpenAI",
        "description": "My personal OpenAI configuration",
        "api_type": "openai",
        "api_url": "https://api.openai.com/v1",
        "api_key": "sk-my-secret-key",
        "enabled": false, // 先创建为禁用状态
        "config": {
            "max_tokens": 4096,
            "temperature": 0.7
        },
        "tags": ["custom", "openai"]
    });

    let create_response = client.post("/model_provider/", custom_provider).await;
    create_response.assert_status(StatusCode::CREATED);
    let provider_id = create_response.json_field("_id").as_str().unwrap().to_string();

    // 步骤5: 用户配置API密钥（更新提供商）
    println!("  5. 配置API密钥...");
    let update_config = json!({
        "api_key": "sk-updated-secret-key",
        "enabled": true, // 启用提供商
        "config": {
            "max_tokens": 2048,
            "temperature": 0.5
        }
    });

    let update_response = client.put(&format!("/model_provider/{}", provider_id), update_config).await;
    update_response.assert_status(StatusCode::OK);

    // 步骤6: 验证提供商配置
    println!("  6. 验证提供商配置...");
    let get_response = client.get(&format!("/model_provider/{}", provider_id)).await;
    get_response.assert_status(StatusCode::OK);
    
    let source = get_response.json_field("_source");
    assert_eq!(source.get("enabled").unwrap().as_bool().unwrap(), true);
    assert_eq!(source.get("name").unwrap().as_str().unwrap(), "My Custom OpenAI");
    
    // 验证敏感字段被过滤
    assert_sensitive_fields_filtered(get_response.json());

    // 步骤7: 用户搜索和过滤提供商
    println!("  7. 搜索启用的提供商...");
    let filter_search = client.post("/model_provider/_search", json!({
        "query": {
            "bool": {
                "filter": [
                    {"term": {"enabled": true}}
                ]
            }
        }
    })).await;
    filter_search.assert_status(StatusCode::OK);
    
    let hits = filter_search.json_field("hits").get("hits").unwrap().as_array().unwrap();
    assert!(!hits.is_empty(), "Should find at least one enabled provider");

    println!("✅ 新用户配置提供商场景测试完成");
    cleanup_test_database(&test_db).await;
}

/// 场景2: 管理员管理提供商的完整流程
#[tokio::test]
async fn test_admin_manage_providers_scenario() {
    let test_db = setup_test_database().await;
    let app = TestApp::builder()
        .with_database()
        .build()
        .await;
    let client = TestClient::new(app.app);

    println!("🧪 测试场景：管理员管理提供商");

    // 步骤1: 创建多个测试提供商
    println!("  1. 创建多个测试提供商...");
    let providers_data = vec![
        json!({
            "name": "OpenAI GPT-4",
            "description": "OpenAI GPT-4 provider",
            "api_type": "openai",
            "api_url": "https://api.openai.com/v1",
            "api_key": "sk-openai-key",
            "enabled": true,
            "tags": ["openai", "gpt-4"]
        }),
        json!({
            "name": "Anthropic Claude",
            "description": "Anthropic Claude provider",
            "api_type": "anthropic",
            "api_url": "https://api.anthropic.com/v1",
            "api_key": "sk-anthropic-key",
            "enabled": true,
            "tags": ["anthropic", "claude"]
        }),
        json!({
            "name": "Local Ollama",
            "description": "Local Ollama provider",
            "api_type": "ollama",
            "api_url": "http://localhost:11434/v1",
            "api_key": "",
            "enabled": false,
            "tags": ["local", "ollama"]
        })
    ];

    let mut provider_ids = Vec::new();
    for provider_data in providers_data {
        let response = client.post("/model_provider/", provider_data).await;
        response.assert_status(StatusCode::CREATED);
        provider_ids.push(response.json_field("_id").as_str().unwrap().to_string());
    }

    // 步骤2: 管理员查看所有提供商
    println!("  2. 查看所有提供商...");
    let all_providers = client.post("/model_provider/_search", json!({
        "query": {"match_all": {}},
        "size": 100,
        "sort": [{"created": {"order": "desc"}}]
    })).await;
    all_providers.assert_status(StatusCode::OK);
    
    let total = all_providers.json_field("hits").get("total").unwrap().as_u64().unwrap();
    assert!(total >= 3, "Should have at least 3 providers");

    // 步骤3: 搜索特定类型的提供商
    println!("  3. 搜索OpenAI类型的提供商...");
    let openai_search = client.post("/model_provider/_search", json!({
        "query": {
            "bool": {
                "filter": [
                    {"term": {"api_type": "openai"}}
                ]
            }
        }
    })).await;
    openai_search.assert_status(StatusCode::OK);

    // 步骤4: 批量更新提供商状态
    println!("  4. 更新提供商配置...");
    for (i, provider_id) in provider_ids.iter().enumerate() {
        let update_data = if i == 2 { // 启用第三个提供商
            json!({"enabled": true, "description": "Updated local provider"})
        } else {
            json!({"description": format!("Updated provider {}", i + 1)})
        };
        
        let response = client.put(&format!("/model_provider/{}", provider_id), update_data).await;
        response.assert_status(StatusCode::OK);
    }

    // 步骤5: 验证更新结果
    println!("  5. 验证更新结果...");
    let enabled_providers = client.post("/model_provider/_search", json!({
        "query": {
            "bool": {
                "filter": [
                    {"term": {"enabled": true}}
                ]
            }
        }
    })).await;
    enabled_providers.assert_status(StatusCode::OK);
    
    let enabled_count = enabled_providers.json_field("hits").get("total").unwrap().as_u64().unwrap();
    assert_eq!(enabled_count, 3, "All providers should now be enabled");

    // 步骤6: 删除自定义提供商（保留内置提供商）
    println!("  6. 删除自定义提供商...");
    for provider_id in &provider_ids {
        let delete_response = client.delete(&format!("/model_provider/{}", provider_id)).await;
        delete_response.assert_status(StatusCode::OK);
    }

    // 步骤7: 验证删除结果
    println!("  7. 验证删除结果...");
    for provider_id in &provider_ids {
        let get_response = client.get(&format!("/model_provider/{}", provider_id)).await;
        get_response.assert_status(StatusCode::NOT_FOUND);
    }

    println!("✅ 管理员管理提供商场景测试完成");
    cleanup_test_database(&test_db).await;
}

/// 场景3: 系统升级和配置刷新场景
#[tokio::test]
async fn test_system_upgrade_config_refresh_scenario() {
    let test_db = setup_test_database().await;
    let app = TestApp::builder()
        .with_database()
        .build()
        .await;
    let client = TestClient::new(app.app);

    println!("🧪 测试场景：系统升级和配置刷新");

    // 步骤1: 系统正常运行，创建一些用户数据
    println!("  1. 创建用户数据...");
    let user_provider = json!({
        "name": "User Custom Provider",
        "description": "User's custom configuration",
        "api_type": "openai",
        "api_url": "https://api.openai.com/v1",
        "api_key": "user-secret-key",
        "enabled": true,
        "config": {
            "max_tokens": 1024,
            "temperature": 0.8
        }
    });

    let create_response = client.post("/model_provider/", user_provider).await;
    create_response.assert_status(StatusCode::CREATED);
    let user_provider_id = create_response.json_field("_id").as_str().unwrap().to_string();

    // 步骤2: 模拟系统重启（重新初始化）
    println!("  2. 模拟系统重启...");
    let init_response = client.post("/setup/_initialize", json!({})).await;
    init_response.assert_status(StatusCode::OK);

    // 步骤3: 验证用户数据保持完整
    println!("  3. 验证用户数据完整性...");
    let get_response = client.get(&format!("/model_provider/{}", user_provider_id)).await;
    get_response.assert_status(StatusCode::OK);
    
    let source = get_response.json_field("_source");
    assert_eq!(source.get("name").unwrap().as_str().unwrap(), "User Custom Provider");
    assert_eq!(source.get("enabled").unwrap().as_bool().unwrap(), true);

    // 步骤4: 验证系统功能正常
    println!("  4. 验证系统功能正常...");
    let health_response = client.get("/_health").await;
    health_response.assert_status(StatusCode::OK);

    let search_response = client.post("/model_provider/_search", json!({
        "query": {"match_all": {}},
        "size": 10
    })).await;
    search_response.assert_status(StatusCode::OK);

    // 步骤5: 测试新功能（如果有的话）
    println!("  5. 测试系统功能完整性...");
    let info_response = client.get("/_info").await;
    info_response.assert_status(StatusCode::OK);

    println!("✅ 系统升级和配置刷新场景测试完成");
    cleanup_test_database(&test_db).await;
}

/// 场景4: 错误恢复和容错场景
#[tokio::test]
async fn test_error_recovery_scenario() {
    let test_db = setup_test_database().await;
    let app = TestApp::builder()
        .with_database()
        .build()
        .await;
    let client = TestClient::new(app.app);

    println!("🧪 测试场景：错误恢复和容错");

    // 步骤1: 测试无效请求的处理
    println!("  1. 测试无效请求处理...");
    let invalid_requests = vec![
        // 无效的JSON
        ("POST", "/model_provider/", "invalid json"),
        // 缺少必需字段
        ("POST", "/model_provider/", r#"{"description": "missing name"}"#),
        // 无效的URL
        ("GET", "/model_provider/invalid-id-format", ""),
    ];

    for (method, path, body) in invalid_requests {
        let response = match method {
            "POST" => {
                if body.is_empty() {
                    client.post(path, json!({})).await
                } else {
                    // 发送原始字符串而不是JSON
                    let request = axum::http::Request::builder()
                        .uri(path)
                        .method("POST")
                        .header("content-type", "application/json")
                        .body(axum::body::Body::from(body.to_string()))
                        .unwrap();
                    
                    let response = client.app.clone().oneshot(request).await.unwrap();
                    TestResponse::new(response).await
                }
            },
            "GET" => client.get(path).await,
            _ => continue,
        };
        
        // 验证错误被正确处理（不是500错误）
        assert!(
            response.status.as_u16() < 500,
            "Invalid request should not cause server error: {} {}",
            method, path
        );
    }

    // 步骤2: 测试并发请求的处理
    println!("  2. 测试并发请求处理...");
    let mut handles = vec![];
    
    for i in 0..5 {
        let client_clone = TestClient::new(app.app.clone());
        let handle = tokio::spawn(async move {
            let provider_data = json!({
                "name": format!("Concurrent Provider {}", i),
                "description": "Concurrent test",
                "api_type": "openai",
                "api_url": "https://api.openai.com/v1",
                "api_key": "test-key",
                "enabled": true
            });
            
            client_clone.post("/model_provider/", provider_data).await
        });
        handles.push(handle);
    }
    
    // 等待所有请求完成
    for handle in handles {
        let response = handle.await.unwrap();
        assert!(
            response.status == StatusCode::CREATED || response.status == StatusCode::CONFLICT,
            "Concurrent request should succeed or conflict"
        );
    }

    // 步骤3: 测试系统在压力下的表现
    println!("  3. 测试系统压力处理...");
    let start_time = std::time::Instant::now();
    
    for i in 0..20 {
        let search_response = client.post("/model_provider/_search", json!({
            "query": {"match_all": {}},
            "size": 10
        })).await;
        
        search_response.assert_status(StatusCode::OK);
        
        // 添加小延迟避免过度压力
        if i % 5 == 0 {
            sleep(Duration::from_millis(10)).await;
        }
    }
    
    let duration = start_time.elapsed();
    assert!(
        duration < Duration::from_secs(10),
        "Stress test should complete within reasonable time: {:?}",
        duration
    );

    println!("✅ 错误恢复和容错场景测试完成");
    cleanup_test_database(&test_db).await;
}
