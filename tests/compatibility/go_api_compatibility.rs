/// Go API兼容性测试
/// 
/// 验证Rust版本API与Go版本的完全兼容性，包括请求/响应格式、错误码等

use axum::http::StatusCode;
use serde_json::{json, Value};

mod common;
use common::{
    assertions::*,
    test_app::TestApp,
    test_data::*,
    test_database::*,
    test_utils::*,
};

/// 测试创建响应格式兼容性
#[tokio::test]
async fn test_create_response_format_compatibility() {
    let test_db = setup_test_database().await;
    let app = TestApp::builder()
        .with_database()
        .build()
        .await;
    let client = TestClient::new(app.app);

    let provider_data = json!({
        "name": "Compatibility Test Provider",
        "description": "Testing Go compatibility",
        "api_type": "openai",
        "api_url": "https://api.openai.com/v1",
        "api_key": "test-key",
        "enabled": true
    });

    let response = client.post("/model_provider/", provider_data).await;
    
    // 验证状态码
    response.assert_status(StatusCode::CREATED);
    
    // 验证Go兼容的响应格式
    let json_response = response.json();
    
    // Go版本的创建响应格式：{"_id": "xxx", "result": "created"}
    assert!(json_response.get("_id").is_some(), "Response should have _id field");
    assert!(json_response.get("result").is_some(), "Response should have result field");
    assert_eq!(
        json_response.get("result").unwrap().as_str().unwrap(),
        "created",
        "Result should be 'created'"
    );
    
    // 验证_id格式（应该是有效的UUID或ID）
    let id = json_response.get("_id").unwrap().as_str().unwrap();
    assert!(!id.is_empty(), "ID should not be empty");
    
    cleanup_test_database(&test_db).await;
}

/// 测试获取响应格式兼容性
#[tokio::test]
async fn test_get_response_format_compatibility() {
    let test_db = setup_test_database().await;
    let app = TestApp::builder()
        .with_database()
        .build()
        .await;
    let client = TestClient::new(app.app);

    // 先创建一个提供商
    let provider_data = json!({
        "name": "Get Compatibility Test",
        "description": "Testing get compatibility",
        "api_type": "openai",
        "api_url": "https://api.openai.com/v1",
        "api_key": "secret-key",
        "enabled": true
    });

    let create_response = client.post("/model_provider/", provider_data).await;
    let provider_id = create_response.json_field("_id").as_str().unwrap();

    // 获取提供商
    let get_response = client.get(&format!("/model_provider/{}", provider_id)).await;
    
    // 验证状态码
    get_response.assert_status(StatusCode::OK);
    
    // 验证Go兼容的响应格式
    let json_response = get_response.json();
    
    // Go版本的获取响应格式：{"_id": "xxx", "found": true, "_source": {...}}
    assert!(json_response.get("_id").is_some(), "Response should have _id field");
    assert!(json_response.get("found").is_some(), "Response should have found field");
    assert!(json_response.get("_source").is_some(), "Response should have _source field");
    
    assert_eq!(
        json_response.get("found").unwrap().as_bool().unwrap(),
        true,
        "Found should be true"
    );
    
    // 验证_source包含完整的提供商数据
    let source = json_response.get("_source").unwrap();
    assert_eq!(source.get("name").unwrap().as_str().unwrap(), "Get Compatibility Test");
    
    // 验证敏感字段被过滤
    assert_sensitive_fields_filtered(json_response);
    
    cleanup_test_database(&test_db).await;
}

/// 测试更新响应格式兼容性
#[tokio::test]
async fn test_update_response_format_compatibility() {
    let test_db = setup_test_database().await;
    let app = TestApp::builder()
        .with_database()
        .build()
        .await;
    let client = TestClient::new(app.app);

    // 先创建一个提供商
    let provider_data = json!({
        "name": "Update Compatibility Test",
        "description": "Original description",
        "api_type": "openai",
        "api_url": "https://api.openai.com/v1",
        "api_key": "test-key",
        "enabled": true
    });

    let create_response = client.post("/model_provider/", provider_data).await;
    let provider_id = create_response.json_field("_id").as_str().unwrap();

    // 更新提供商
    let update_data = json!({
        "description": "Updated description",
        "enabled": false
    });

    let update_response = client.put(&format!("/model_provider/{}", provider_id), update_data).await;
    
    // 验证状态码
    update_response.assert_status(StatusCode::OK);
    
    // 验证Go兼容的响应格式
    let json_response = update_response.json();
    
    // Go版本的更新响应格式：{"_id": "xxx", "result": "updated"}
    assert!(json_response.get("_id").is_some(), "Response should have _id field");
    assert!(json_response.get("result").is_some(), "Response should have result field");
    assert_eq!(
        json_response.get("result").unwrap().as_str().unwrap(),
        "updated",
        "Result should be 'updated'"
    );
    
    cleanup_test_database(&test_db).await;
}

/// 测试删除响应格式兼容性
#[tokio::test]
async fn test_delete_response_format_compatibility() {
    let test_db = setup_test_database().await;
    let app = TestApp::builder()
        .with_database()
        .build()
        .await;
    let client = TestClient::new(app.app);

    // 先创建一个提供商
    let provider_data = json!({
        "name": "Delete Compatibility Test",
        "description": "To be deleted",
        "api_type": "openai",
        "api_url": "https://api.openai.com/v1",
        "api_key": "test-key",
        "enabled": true
    });

    let create_response = client.post("/model_provider/", provider_data).await;
    let provider_id = create_response.json_field("_id").as_str().unwrap();

    // 删除提供商
    let delete_response = client.delete(&format!("/model_provider/{}", provider_id)).await;
    
    // 验证状态码
    delete_response.assert_status(StatusCode::OK);
    
    // 验证Go兼容的响应格式
    let json_response = delete_response.json();
    
    // Go版本的删除响应格式：{"_id": "xxx", "result": "deleted"}
    assert!(json_response.get("_id").is_some(), "Response should have _id field");
    assert!(json_response.get("result").is_some(), "Response should have result field");
    assert_eq!(
        json_response.get("result").unwrap().as_str().unwrap(),
        "deleted",
        "Result should be 'deleted'"
    );
    
    cleanup_test_database(&test_db).await;
}

/// 测试搜索响应格式兼容性
#[tokio::test]
async fn test_search_response_format_compatibility() {
    let test_db = setup_test_database().await;
    let app = TestApp::builder()
        .with_database()
        .build()
        .await;
    let client = TestClient::new(app.app);

    // 创建测试数据
    let providers = create_search_test_providers();
    for provider in &providers {
        test_db.insert_test_data("model_provider", provider).await.unwrap();
    }

    // 执行搜索
    let search_query = json!({
        "query": {
            "match_all": {}
        },
        "size": 10
    });

    let response = client.post("/model_provider/_search", search_query).await;
    
    // 验证状态码
    response.assert_status(StatusCode::OK);
    
    // 验证Go兼容的搜索响应格式
    let json_response = response.json();
    
    // Go版本的搜索响应格式：{"hits": {"total": N, "hits": [...]}}
    assert!(json_response.get("hits").is_some(), "Response should have hits field");
    
    let hits = json_response.get("hits").unwrap();
    assert!(hits.get("total").is_some(), "Hits should have total field");
    assert!(hits.get("hits").is_some(), "Hits should have hits array");
    
    // 验证hits数组格式
    let hits_array = hits.get("hits").unwrap().as_array().unwrap();
    if !hits_array.is_empty() {
        let first_hit = &hits_array[0];
        assert!(first_hit.get("_id").is_some(), "Hit should have _id field");
        assert!(first_hit.get("_source").is_some(), "Hit should have _source field");
        
        // 验证敏感字段被过滤
        assert_sensitive_fields_filtered(first_hit);
    }
    
    cleanup_test_database(&test_db).await;
}

/// 测试错误响应格式兼容性
#[tokio::test]
async fn test_error_response_format_compatibility() {
    let test_db = setup_test_database().await;
    let app = TestApp::builder()
        .with_database()
        .build()
        .await;
    let client = TestClient::new(app.app);

    // 测试404错误
    let response = client.get("/model_provider/nonexistent-id").await;
    response.assert_status(StatusCode::NOT_FOUND);
    
    let json_response = response.json();
    assert!(json_response.get("error").is_some(), "Error response should have error field");
    
    // 测试400错误（无效数据）
    let invalid_data = json!({
        "name": "", // 空名称应该导致验证错误
        "api_type": "invalid"
    });
    
    let response = client.post("/model_provider/", invalid_data).await;
    response.assert_status(StatusCode::BAD_REQUEST);
    
    let json_response = response.json();
    assert!(json_response.get("error").is_some(), "Validation error should have error field");
    
    cleanup_test_database(&test_db).await;
}

/// 测试时间戳格式兼容性
#[tokio::test]
async fn test_timestamp_format_compatibility() {
    let test_db = setup_test_database().await;
    let app = TestApp::builder()
        .with_database()
        .build()
        .await;
    let client = TestClient::new(app.app);

    // 创建提供商
    let provider_data = json!({
        "name": "Timestamp Test Provider",
        "description": "Testing timestamp format",
        "api_type": "openai",
        "api_url": "https://api.openai.com/v1",
        "api_key": "test-key",
        "enabled": true
    });

    let create_response = client.post("/model_provider/", provider_data).await;
    let provider_id = create_response.json_field("_id").as_str().unwrap();

    // 获取提供商并检查时间戳格式
    let get_response = client.get(&format!("/model_provider/{}", provider_id)).await;
    let source = get_response.json_field("_source");
    
    // 验证时间戳字段存在
    assert!(source.get("created").is_some(), "Should have created timestamp");
    assert!(source.get("updated").is_some(), "Should have updated timestamp");
    
    // 验证时间戳格式（应该是ISO 8601格式）
    let created = source.get("created").unwrap().as_str().unwrap();
    let updated = source.get("updated").unwrap().as_str().unwrap();
    
    // 简单验证时间戳格式（包含T和Z）
    assert!(created.contains('T'), "Created timestamp should be in ISO format");
    assert!(updated.contains('T'), "Updated timestamp should be in ISO format");
    
    cleanup_test_database(&test_db).await;
}

/// 测试字段命名一致性
#[tokio::test]
async fn test_field_naming_consistency() {
    let test_db = setup_test_database().await;
    let app = TestApp::builder()
        .with_database()
        .build()
        .await;
    let client = TestClient::new(app.app);

    // 创建提供商
    let provider_data = json!({
        "name": "Field Test Provider",
        "description": "Testing field naming",
        "api_type": "openai",
        "api_url": "https://api.openai.com/v1",
        "api_key": "test-key",
        "enabled": true,
        "builtin": false
    });

    let create_response = client.post("/model_provider/", provider_data).await;
    let provider_id = create_response.json_field("_id").as_str().unwrap();

    // 获取提供商并验证字段命名
    let get_response = client.get(&format!("/model_provider/{}", provider_id)).await;
    let source = get_response.json_field("_source");
    
    // 验证关键字段存在且命名正确
    let expected_fields = vec![
        "name", "description", "api_type", "api_url", 
        "enabled", "builtin", "created", "updated"
    ];
    
    for field in expected_fields {
        assert!(
            source.get(field).is_some(),
            "Field '{}' should exist in response",
            field
        );
    }
    
    cleanup_test_database(&test_db).await;
}
