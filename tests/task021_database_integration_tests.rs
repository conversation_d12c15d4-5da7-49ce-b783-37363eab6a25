/// 数据库集成测试 - TASK-021
///
/// 测试SurrealDB数据库连接、数据一致性、并发操作等

use std::time::Duration;
use std::sync::Arc;

use coco_server::{
    config::app_config::DatabaseConfig,
    database::client::SurrealDBClient,
};

/// 测试数据库客户端创建
#[tokio::test]
#[ignore] // 需要运行中的SurrealDB实例
async fn test_database_client_creation() {
    let db_config = DatabaseConfig::default();
    let result = SurrealDBClient::new(db_config).await;

    // 如果SurrealDB服务运行，连接应该成功
    // 如果服务未运行，会返回错误，这是预期的
    match result {
        Ok(_client) => {
            println!("✅ 数据库连接成功");
        }
        Err(e) => {
            println!("⚠️ 数据库连接失败（可能SurrealDB服务未运行）: {}", e);
            // 这不是测试失败，只是服务未运行
        }
    }
}

/// 测试数据库配置验证
#[tokio::test]
async fn test_database_config_validation() {
    // 测试默认配置
    let default_config = DatabaseConfig::default();
    assert!(!default_config.url.is_empty());
    assert!(!default_config.username.is_empty());
    assert!(!default_config.password.is_empty());
    assert!(!default_config.namespace.is_empty());
    assert!(!default_config.database.is_empty());

    println!("✅ 数据库配置验证通过");
}

/// 测试并发数据库客户端创建
#[tokio::test]
async fn test_concurrent_client_creation() {
    let mut handles = vec![];

    // 创建多个并发任务来测试客户端创建的线程安全性
    for i in 0..3 {
        let handle = tokio::spawn(async move {
            let db_config = DatabaseConfig::default();
            let result = SurrealDBClient::new(db_config).await;

            match result {
                Ok(_client) => {
                    println!("✅ 并发客户端 {} 创建成功", i);
                    true
                }
                Err(e) => {
                    println!("⚠️ 并发客户端 {} 创建失败: {}", i, e);
                    false
                }
            }
        });

        handles.push(handle);
    }

    // 等待所有任务完成
    let mut success_count = 0;
    for handle in handles {
        if handle.await.unwrap() {
            success_count += 1;
        }
    }

    println!("✅ 并发测试完成，成功创建 {} 个客户端", success_count);
}

/// 测试数据库客户端性能
#[tokio::test]
async fn test_database_client_performance() {
    let start_time = std::time::Instant::now();

    // 测试客户端创建性能
    for _i in 0..3 {
        let db_config = DatabaseConfig::default();
        let _result = SurrealDBClient::new(db_config).await;
        // 不检查结果，只测试性能
    }

    let creation_duration = start_time.elapsed();

    // 验证客户端创建性能（应该在合理时间内完成）
    assert!(
        creation_duration < Duration::from_secs(10),
        "Client creation should complete within 10 seconds, took {:?}",
        creation_duration
    );

    println!("✅ 数据库客户端性能测试完成，耗时: {:?}", creation_duration);
}
