/// 数据库集成测试
/// 
/// 测试SurrealDB数据库连接、数据一致性、并发操作等

use std::time::Duration;
use tokio::time::timeout;

mod common;
use common::{
    test_data::*,
    test_database::*,
    test_utils::*,
};

use coco_server::{
    database::{init_database, DB},
    models::model_provider::ModelProvider,
};

/// 测试数据库连接和断开
#[tokio::test]
async fn test_database_connection() {
    let test_db = TestDatabase::new();
    
    // 测试连接
    let result = test_db.setup().await;
    assert!(result.is_ok(), "Database connection should succeed");
    
    // 测试连接检查
    let is_connected = test_db.check_connection().await.unwrap();
    assert!(is_connected, "Database should be connected");
    
    cleanup_test_database(&test_db).await;
}

/// 测试表结构创建和验证
#[tokio::test]
async fn test_table_schema_creation() {
    let test_db = setup_test_database().await;
    
    // 验证model_provider表存在
    let result: Vec<surrealdb::sql::Value> = DB
        .query("INFO FOR TABLE model_provider")
        .await
        .expect("Failed to query table info");
    
    assert!(!result.is_empty(), "model_provider table should exist");
    
    // 验证access_token表存在
    let result: Vec<surrealdb::sql::Value> = DB
        .query("INFO FOR TABLE access_token")
        .await
        .expect("Failed to query table info");
    
    assert!(!result.is_empty(), "access_token table should exist");
    
    cleanup_test_database(&test_db).await;
}

/// 测试数据一致性
#[tokio::test]
async fn test_data_consistency() {
    let test_db = setup_test_database().await;
    
    // 创建测试提供商
    let provider = create_test_model_provider("Consistency Test", false);
    
    // 插入数据
    test_db.insert_test_data("model_provider", &provider).await.unwrap();
    
    // 验证数据存在
    let result: Vec<ModelProvider> = DB
        .query("SELECT * FROM model_provider WHERE name = 'Consistency Test'")
        .await
        .unwrap();
    
    assert_eq!(result.len(), 1, "Should find exactly one provider");
    assert_eq!(result[0].name, "Consistency Test");
    assert_eq!(result[0].api_type, "openai");
    
    // 更新数据
    let _: Vec<surrealdb::sql::Value> = DB
        .query("UPDATE model_provider SET description = 'Updated description' WHERE name = 'Consistency Test'")
        .await
        .unwrap();
    
    // 验证更新
    let updated_result: Vec<ModelProvider> = DB
        .query("SELECT * FROM model_provider WHERE name = 'Consistency Test'")
        .await
        .unwrap();
    
    assert_eq!(updated_result[0].description, "Updated description");
    
    cleanup_test_database(&test_db).await;
}

/// 测试并发操作安全性
#[tokio::test]
async fn test_concurrent_operations() {
    let test_db = setup_test_database().await;
    
    // 创建多个并发任务
    let mut handles = vec![];
    
    for i in 0..10 {
        let provider = create_test_model_provider(&format!("Concurrent Provider {}", i), false);
        
        let handle = tokio::spawn(async move {
            // 插入数据
            let _: Option<ModelProvider> = DB
                .create("model_provider")
                .content(provider)
                .await
                .expect("Failed to create provider");
        });
        
        handles.push(handle);
    }
    
    // 等待所有任务完成
    for handle in handles {
        handle.await.expect("Task should complete successfully");
    }
    
    // 验证所有数据都被正确插入
    let count = test_db.count_records("model_provider").await.unwrap();
    assert_eq!(count, 10, "Should have 10 providers");
    
    cleanup_test_database(&test_db).await;
}

/// 测试事务处理
#[tokio::test]
async fn test_transaction_handling() {
    let test_db = setup_test_database().await;
    
    // 开始事务
    let _: Vec<surrealdb::sql::Value> = DB
        .query("BEGIN TRANSACTION")
        .await
        .expect("Failed to begin transaction");
    
    // 在事务中插入数据
    let provider1 = create_test_model_provider("Transaction Test 1", false);
    let provider2 = create_test_model_provider("Transaction Test 2", false);
    
    let _: Option<ModelProvider> = DB
        .create("model_provider")
        .content(provider1)
        .await
        .expect("Failed to create provider 1");
    
    let _: Option<ModelProvider> = DB
        .create("model_provider")
        .content(provider2)
        .await
        .expect("Failed to create provider 2");
    
    // 提交事务
    let _: Vec<surrealdb::sql::Value> = DB
        .query("COMMIT TRANSACTION")
        .await
        .expect("Failed to commit transaction");
    
    // 验证数据存在
    let count = test_db.count_records("model_provider").await.unwrap();
    assert_eq!(count, 2, "Should have 2 providers after commit");
    
    cleanup_test_database(&test_db).await;
}

/// 测试事务回滚
#[tokio::test]
async fn test_transaction_rollback() {
    let test_db = setup_test_database().await;
    
    // 先插入一些基础数据
    let base_provider = create_test_model_provider("Base Provider", false);
    test_db.insert_test_data("model_provider", &base_provider).await.unwrap();
    
    let initial_count = test_db.count_records("model_provider").await.unwrap();
    
    // 开始事务
    let _: Vec<surrealdb::sql::Value> = DB
        .query("BEGIN TRANSACTION")
        .await
        .expect("Failed to begin transaction");
    
    // 在事务中插入数据
    let provider1 = create_test_model_provider("Rollback Test 1", false);
    let provider2 = create_test_model_provider("Rollback Test 2", false);
    
    let _: Option<ModelProvider> = DB
        .create("model_provider")
        .content(provider1)
        .await
        .expect("Failed to create provider 1");
    
    let _: Option<ModelProvider> = DB
        .create("model_provider")
        .content(provider2)
        .await
        .expect("Failed to create provider 2");
    
    // 回滚事务
    let _: Vec<surrealdb::sql::Value> = DB
        .query("CANCEL TRANSACTION")
        .await
        .expect("Failed to rollback transaction");
    
    // 验证数据没有被插入
    let final_count = test_db.count_records("model_provider").await.unwrap();
    assert_eq!(final_count, initial_count, "Count should remain unchanged after rollback");
    
    cleanup_test_database(&test_db).await;
}

/// 测试数据库性能
#[tokio::test]
async fn test_database_performance() {
    let test_db = setup_test_database().await;
    
    // 测试批量插入性能
    let start_time = std::time::Instant::now();
    
    for i in 0..100 {
        let provider = create_test_model_provider(&format!("Performance Test {}", i), false);
        let _: Option<ModelProvider> = DB
            .create("model_provider")
            .content(provider)
            .await
            .expect("Failed to create provider");
    }
    
    let insert_duration = start_time.elapsed();
    
    // 验证插入性能（应该在合理时间内完成）
    assert!(
        insert_duration < Duration::from_secs(10),
        "Batch insert should complete within 10 seconds, took {:?}",
        insert_duration
    );
    
    // 测试查询性能
    let query_start = std::time::Instant::now();
    
    let _: Vec<ModelProvider> = DB
        .query("SELECT * FROM model_provider WHERE enabled = true")
        .await
        .expect("Failed to query providers");
    
    let query_duration = query_start.elapsed();
    
    // 验证查询性能
    assert!(
        query_duration < Duration::from_millis(500),
        "Query should complete within 500ms, took {:?}",
        query_duration
    );
    
    cleanup_test_database(&test_db).await;
}

/// 测试数据库连接超时处理
#[tokio::test]
async fn test_database_timeout_handling() {
    let test_db = setup_test_database().await;
    
    // 测试查询超时
    let query_future = DB.query("SELECT * FROM model_provider");
    
    let result = timeout(Duration::from_secs(5), query_future).await;
    
    assert!(result.is_ok(), "Query should complete within timeout");
    
    cleanup_test_database(&test_db).await;
}

/// 测试数据库错误处理
#[tokio::test]
async fn test_database_error_handling() {
    let test_db = setup_test_database().await;
    
    // 测试无效查询
    let result = DB.query("INVALID SQL QUERY").await;
    assert!(result.is_err(), "Invalid query should return error");
    
    // 测试插入无效数据
    let invalid_data = serde_json::json!({
        "invalid_field": "invalid_value"
    });
    
    let result: Result<Option<serde_json::Value>, _> = DB
        .create("model_provider")
        .content(invalid_data)
        .await;
    
    // 根据数据库配置，这可能成功或失败
    // 主要是测试错误处理不会导致程序崩溃
    
    cleanup_test_database(&test_db).await;
}
