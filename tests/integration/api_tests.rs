/// API集成测试
///
/// 测试所有API端点的功能正确性，包括CRUD操作、搜索功能、错误处理等

use axum::http::StatusCode;
use serde_json::json;

mod common;
use common::{
    assertions::*,
    test_app::TestApp,
    test_data::*,
    test_database::*,
    test_utils::*,
};

/// 测试模型提供商创建API
#[tokio::test]
async fn test_create_model_provider_success() {
    let test_db = setup_test_database().await;
    let app = TestApp::builder()
        .with_database()
        .build()
        .await;
    let client = TestClient::new(app.app);

    // 创建测试数据
    let provider_data = json!({
        "name": "Test Provider",
        "description": "A test provider",
        "api_type": "openai",
        "api_url": "https://api.openai.com/v1",
        "api_key": "test-key",
        "enabled": true
    });

    // 发送创建请求
    let response = client.post("/model_provider/", provider_data).await;

    // 验证响应
    response.assert_status(StatusCode::CREATED);
    assert_go_compatible_response(response.json(), "create");

    // 验证返回的ID
    let provider_id = response.json_field("_id").as_str().unwrap();
    assert!(!provider_id.is_empty(), "Provider ID should not be empty");

    cleanup_test_database(&test_db).await;
}

/// 测试创建重复名称的提供商
#[tokio::test]
async fn test_create_model_provider_duplicate_name() {
    let test_db = setup_test_database().await;
    let app = TestApp::builder()
        .with_database()
        .build()
        .await;
    let client = TestClient::new(app.app);

    let provider_data = json!({
        "name": "Duplicate Provider",
        "description": "First provider",
        "api_type": "openai",
        "api_url": "https://api.openai.com/v1",
        "api_key": "test-key",
        "enabled": true
    });

    // 第一次创建应该成功
    let response1 = client.post("/model_provider/", provider_data.clone()).await;
    response1.assert_status(StatusCode::CREATED);

    // 第二次创建应该失败
    let response2 = client.post("/model_provider/", provider_data).await;
    response2.assert_status(StatusCode::CONFLICT);
    assert_validation_error(response2.json(), Some("name"));

    cleanup_test_database(&test_db).await;
}

/// 测试创建无效数据的提供商
#[tokio::test]
async fn test_create_model_provider_invalid_data() {
    let test_db = setup_test_database().await;
    let app = TestApp::builder()
        .with_database()
        .build()
        .await;
    let client = TestClient::new(app.app);

    // 测试空名称
    let invalid_data = json!({
        "name": "",
        "description": "Invalid provider",
        "api_type": "openai",
        "api_url": "https://api.openai.com/v1",
        "enabled": true
    });

    let response = client.post("/model_provider/", invalid_data).await;
    response.assert_status(StatusCode::BAD_REQUEST);
    assert_validation_error(response.json(), Some("name"));

    cleanup_test_database(&test_db).await;
}

/// 测试获取模型提供商
#[tokio::test]
async fn test_get_model_provider_success() {
    let test_db = setup_test_database().await;
    let app = TestApp::builder()
        .with_database()
        .build()
        .await;
    let client = TestClient::new(app.app);

    // 先创建一个提供商
    let provider_data = json!({
        "name": "Get Test Provider",
        "description": "Provider for get test",
        "api_type": "openai",
        "api_url": "https://api.openai.com/v1",
        "api_key": "secret-key",
        "enabled": true
    });

    let create_response = client.post("/model_provider/", provider_data).await;
    create_response.assert_status(StatusCode::CREATED);

    let provider_id = create_response.json_field("_id").as_str().unwrap();

    // 获取提供商
    let get_response = client.get(&format!("/model_provider/{}", provider_id)).await;

    // 验证响应
    get_response.assert_status(StatusCode::OK);
    assert_go_compatible_response(get_response.json(), "get");

    // 验证敏感字段被过滤
    assert_sensitive_fields_filtered(get_response.json());

    // 验证数据正确性
    let source = get_response.json_field("_source");
    assert_eq!(source.get("name").unwrap().as_str().unwrap(), "Get Test Provider");

    cleanup_test_database(&test_db).await;
}

/// 测试获取不存在的提供商
#[tokio::test]
async fn test_get_model_provider_not_found() {
    let test_db = setup_test_database().await;
    let app = TestApp::builder()
        .with_database()
        .build()
        .await;
    let client = TestClient::new(app.app);

    let response = client.get("/model_provider/nonexistent-id").await;
    response.assert_status(StatusCode::NOT_FOUND);

    cleanup_test_database(&test_db).await;
}

/// 测试更新模型提供商
#[tokio::test]
async fn test_update_model_provider_success() {
    let test_db = setup_test_database().await;
    let app = TestApp::builder()
        .with_database()
        .build()
        .await;
    let client = TestClient::new(app.app);

    // 先创建一个提供商
    let provider_data = json!({
        "name": "Update Test Provider",
        "description": "Original description",
        "api_type": "openai",
        "api_url": "https://api.openai.com/v1",
        "api_key": "original-key",
        "enabled": true
    });

    let create_response = client.post("/model_provider/", provider_data).await;
    let provider_id = create_response.json_field("_id").as_str().unwrap();

    // 更新提供商
    let update_data = json!({
        "description": "Updated description",
        "enabled": false,
        "api_key": "new-secret-key"
    });

    let update_response = client.put(&format!("/model_provider/{}", provider_id), update_data).await;

    // 验证响应
    update_response.assert_status(StatusCode::OK);
    assert_go_compatible_response(update_response.json(), "update");

    cleanup_test_database(&test_db).await;
}

/// 测试更新内置提供商保护
#[tokio::test]
async fn test_update_builtin_provider_protection() {
    let test_db = setup_test_database().await;
    let app = TestApp::builder()
        .with_database()
        .build()
        .await;
    let client = TestClient::new(app.app);

    // 创建内置提供商
    let builtin_provider = create_builtin_test_provider();
    test_db.insert_test_data("model_provider", &builtin_provider).await.unwrap();

    // 尝试更新内置提供商的name字段
    let update_data = json!({
        "name": "Modified Builtin Name"
    });

    let response = client.put(&format!("/model_provider/{}", builtin_provider.id), update_data).await;

    // 应该被拒绝
    response.assert_status(StatusCode::FORBIDDEN);
    assert_builtin_protection_error(response.json());

    cleanup_test_database(&test_db).await;
}

/// 测试删除模型提供商
#[tokio::test]
async fn test_delete_model_provider_success() {
    let test_db = setup_test_database().await;
    let app = TestApp::builder()
        .with_database()
        .build()
        .await;
    let client = TestClient::new(app.app);

    // 先创建一个自定义提供商
    let provider_data = json!({
        "name": "Delete Test Provider",
        "description": "Provider for delete test",
        "api_type": "openai",
        "api_url": "https://api.openai.com/v1",
        "api_key": "test-key",
        "enabled": true
    });

    let create_response = client.post("/model_provider/", provider_data).await;
    let provider_id = create_response.json_field("_id").as_str().unwrap();

    // 删除提供商
    let delete_response = client.delete(&format!("/model_provider/{}", provider_id)).await;

    // 验证响应
    delete_response.assert_status(StatusCode::OK);
    assert_go_compatible_response(delete_response.json(), "delete");

    // 验证提供商已被删除
    let get_response = client.get(&format!("/model_provider/{}", provider_id)).await;
    get_response.assert_status(StatusCode::NOT_FOUND);

    cleanup_test_database(&test_db).await;
}

/// 测试删除内置提供商保护
#[tokio::test]
async fn test_delete_builtin_provider_forbidden() {
    let test_db = setup_test_database().await;
    let app = TestApp::builder()
        .with_database()
        .build()
        .await;
    let client = TestClient::new(app.app);

    // 创建内置提供商
    let builtin_provider = create_builtin_test_provider();
    test_db.insert_test_data("model_provider", &builtin_provider).await.unwrap();

    // 尝试删除内置提供商
    let response = client.delete(&format!("/model_provider/{}", builtin_provider.id)).await;

    // 应该被拒绝
    response.assert_status(StatusCode::FORBIDDEN);
    assert_builtin_protection_error(response.json());

    cleanup_test_database(&test_db).await;
}

/// 测试基础搜索功能
#[tokio::test]
async fn test_search_providers_basic() {
    let test_db = setup_test_database().await;
    let app = TestApp::builder()
        .with_database()
        .build()
        .await;
    let client = TestClient::new(app.app);

    // 创建多个测试提供商
    let providers = create_search_test_providers();
    for provider in &providers {
        test_db.insert_test_data("model_provider", provider).await.unwrap();
    }

    // 执行基础搜索
    let search_query = json!({
        "query": {
            "match_all": {}
        },
        "size": 10
    });

    let response = client.post("/model_provider/_search", search_query).await;

    // 验证响应
    response.assert_status(StatusCode::OK);
    assert_go_compatible_response(response.json(), "search");

    // 验证搜索结果
    let hits = response.json_field("hits");
    let total = hits.get("total").unwrap().as_u64().unwrap();
    assert!(total >= providers.len() as u64, "Should find all created providers");

    cleanup_test_database(&test_db).await;
}

/// 测试带过滤条件的搜索
#[tokio::test]
async fn test_search_providers_with_filters() {
    let test_db = setup_test_database().await;
    let app = TestApp::builder()
        .with_database()
        .build()
        .await;
    let client = TestClient::new(app.app);

    // 创建测试提供商
    let providers = create_search_test_providers();
    for provider in &providers {
        test_db.insert_test_data("model_provider", provider).await.unwrap();
    }

    // 搜索启用的提供商
    let search_query = json!({
        "query": {
            "bool": {
                "filter": [
                    {
                        "term": {
                            "enabled": true
                        }
                    }
                ]
            }
        }
    });

    let response = client.post("/model_provider/_search", search_query).await;
    response.assert_status(StatusCode::OK);

    // 验证所有结果都是启用的
    let hits_array = response.json_field("hits").get("hits").unwrap().as_array().unwrap();
    for hit in hits_array {
        let source = hit.get("_source").unwrap();
        assert_eq!(source.get("enabled").unwrap().as_bool().unwrap(), true);
    }

    cleanup_test_database(&test_db).await;
}

/// 测试分页功能
#[tokio::test]
async fn test_search_providers_pagination() {
    let test_db = setup_test_database().await;
    let app = TestApp::builder()
        .with_database()
        .build()
        .await;
    let client = TestClient::new(app.app);

    // 创建多个测试提供商
    let providers = create_multiple_test_providers(5);
    for provider in &providers {
        test_db.insert_test_data("model_provider", provider).await.unwrap();
    }

    // 测试分页
    let search_query = create_pagination_query(2, 1);
    let response = client.post("/model_provider/_search", search_query).await;

    response.assert_status(StatusCode::OK);
    assert_pagination_response(response.json(), 2, 1);

    cleanup_test_database(&test_db).await;
}
