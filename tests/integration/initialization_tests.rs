/// 初始化系统集成测试
/// 
/// 测试配置文件加载、内置提供商导入、系统启动等初始化功能

use std::path::PathBuf;
use tempfile::TempDir;
use tokio::fs;

mod common;
use common::{
    test_data::*,
    test_database::*,
    test_utils::*,
};

use coco_server::{
    config::{
        app_config::AppConfig,
        config_manager::ConfigManager,
    },
    database::DB,
    models::model_provider::ModelProvider,
    services::import_strategy::{ImportAction, ImportStrategy},
};

/// 测试配置文件加载
#[tokio::test]
async fn test_config_file_loading() {
    // 创建临时配置文件
    let temp_dir = TempDir::new().unwrap();
    let config_path = temp_dir.path().join("test_config.toml");
    
    let config_content = r#"
[server]
host = "127.0.0.1"
port = 8080

[database]
url = "memory"
username = "root"
password = "root"
namespace = "test"
database = "test"

[jwt]
secret = "test-secret"
expiration = 3600

[cache]
ttl = 300
max_size = 1000

[log]
level = "debug"
format = "json"
"#;
    
    fs::write(&config_path, config_content).await.unwrap();
    
    // 测试配置加载
    let config = ConfigManager::load_from_file(&config_path).await;
    assert!(config.is_ok(), "Config loading should succeed");
    
    let config = config.unwrap();
    assert_eq!(config.server.host, "127.0.0.1");
    assert_eq!(config.server.port, 8080);
    assert_eq!(config.database.url, "memory");
    assert_eq!(config.jwt.secret, "test-secret");
}

/// 测试无效配置文件处理
#[tokio::test]
async fn test_invalid_config_file_handling() {
    let temp_dir = TempDir::new().unwrap();
    let config_path = temp_dir.path().join("invalid_config.toml");
    
    // 创建无效的配置文件
    let invalid_config = r#"
[server
host = "127.0.0.1"  # 缺少闭合括号
port = "invalid_port"  # 端口应该是数字
"#;
    
    fs::write(&config_path, invalid_config).await.unwrap();
    
    // 测试配置加载失败
    let result = ConfigManager::load_from_file(&config_path).await;
    assert!(result.is_err(), "Invalid config should fail to load");
}

/// 测试内置提供商导入
#[tokio::test]
async fn test_builtin_provider_import() {
    let test_db = setup_test_database().await;
    
    // 创建测试配置文件
    let temp_dir = TempDir::new().unwrap();
    let config_path = temp_dir.path().join("providers.toml");
    
    let provider_config = r#"
[[providers]]
name = "OpenAI"
description = "Official OpenAI provider"
api_type = "openai"
api_url = "https://api.openai.com/v1"
enabled = true
builtin = true

[[providers]]
name = "Anthropic"
description = "Anthropic Claude provider"
api_type = "anthropic"
api_url = "https://api.anthropic.com/v1"
enabled = true
builtin = true
"#;
    
    fs::write(&config_path, provider_config).await.unwrap();
    
    // 模拟导入过程
    let providers = load_providers_from_config(&config_path).await.unwrap();
    assert_eq!(providers.len(), 2);
    
    // 导入到数据库
    for provider in providers {
        let _: Option<ModelProvider> = DB
            .create("model_provider")
            .content(provider)
            .await
            .expect("Failed to import provider");
    }
    
    // 验证导入结果
    let count = test_db.count_records("model_provider").await.unwrap();
    assert_eq!(count, 2, "Should have imported 2 providers");
    
    // 验证内置标记
    let builtin_providers: Vec<ModelProvider> = DB
        .query("SELECT * FROM model_provider WHERE builtin = true")
        .await
        .unwrap();
    
    assert_eq!(builtin_providers.len(), 2, "All imported providers should be builtin");
    
    cleanup_test_database(&test_db).await;
}

/// 测试重复导入防护
#[tokio::test]
async fn test_duplicate_import_protection() {
    let test_db = setup_test_database().await;
    
    // 创建测试提供商
    let provider = create_builtin_test_provider();
    
    // 第一次导入
    let _: Option<ModelProvider> = DB
        .create("model_provider")
        .content(&provider)
        .await
        .expect("Failed to import provider");
    
    let initial_count = test_db.count_records("model_provider").await.unwrap();
    
    // 第二次导入相同提供商（应该被跳过）
    let import_strategy = ImportStrategy::SkipExisting;
    let action = determine_import_action(&provider, Some(&provider), &import_strategy);
    
    match action {
        ImportAction::Skip => {
            // 正确行为：跳过重复导入
        }
        _ => panic!("Should skip duplicate import"),
    }
    
    // 验证记录数量没有增加
    let final_count = test_db.count_records("model_provider").await.unwrap();
    assert_eq!(final_count, initial_count, "Count should not increase for duplicate import");
    
    cleanup_test_database(&test_db).await;
}

/// 测试配置热重载
#[tokio::test]
async fn test_config_hot_reload() {
    let temp_dir = TempDir::new().unwrap();
    let config_path = temp_dir.path().join("reload_config.toml");
    
    // 创建初始配置
    let initial_config = r#"
[server]
host = "127.0.0.1"
port = 8080

[log]
level = "info"
"#;
    
    fs::write(&config_path, initial_config).await.unwrap();
    
    // 加载初始配置
    let config_manager = ConfigManager::new(
        ConfigManager::load_from_file(&config_path).await.unwrap()
    );
    
    assert_eq!(config_manager.get_config().log.level, "info");
    
    // 修改配置文件
    let updated_config = r#"
[server]
host = "127.0.0.1"
port = 8080

[log]
level = "debug"
"#;
    
    fs::write(&config_path, updated_config).await.unwrap();
    
    // 测试重载（这里只是测试配置文件的变化检测）
    let new_config = ConfigManager::load_from_file(&config_path).await.unwrap();
    assert_eq!(new_config.log.level, "debug");
}

/// 测试初始化错误恢复
#[tokio::test]
async fn test_initialization_error_recovery() {
    // 测试数据库连接失败的恢复
    let invalid_config = AppConfig {
        server: coco_server::config::app_config::ServerConfig {
            host: "127.0.0.1".to_string(),
            port: 8080,
            tls: None,
        },
        database: coco_server::config::app_config::DatabaseConfig {
            url: "invalid://invalid:9999".to_string(), // 无效的数据库URL
            username: "root".to_string(),
            password: "root".to_string(),
            namespace: "test".to_string(),
            database: "test".to_string(),
        },
        jwt: coco_server::config::app_config::JwtConfig {
            secret: "test-secret".to_string(),
            expiration: 3600,
        },
        cache: coco_server::config::app_config::CacheConfig {
            ttl: 300,
            max_size: 1000,
        },
        log: coco_server::config::app_config::LogConfig {
            level: "debug".to_string(),
            format: "json".to_string(),
        },
    };
    
    // 尝试初始化数据库（应该失败）
    let result = coco_server::database::init_database(&invalid_config.database).await;
    assert!(result.is_err(), "Invalid database config should fail");
    
    // 验证错误处理不会导致程序崩溃
    // 实际应用中，这里应该有优雅的错误处理和重试机制
}

/// 测试系统状态验证
#[tokio::test]
async fn test_system_status_validation() {
    let test_db = setup_test_database().await;
    
    // 验证数据库连接状态
    let is_connected = test_db.check_connection().await.unwrap();
    assert!(is_connected, "Database should be connected");
    
    // 验证必要的表存在
    let tables = vec!["model_provider", "access_token"];
    
    for table in tables {
        let result: Vec<surrealdb::sql::Value> = DB
            .query(&format!("INFO FOR TABLE {}", table))
            .await
            .expect("Failed to query table info");
        
        assert!(!result.is_empty(), "Table {} should exist", table);
    }
    
    cleanup_test_database(&test_db).await;
}

// 辅助函数

/// 从配置文件加载提供商
async fn load_providers_from_config(config_path: &PathBuf) -> Result<Vec<ModelProvider>, Box<dyn std::error::Error>> {
    let content = fs::read_to_string(config_path).await?;
    let config: toml::Value = toml::from_str(&content)?;
    
    let mut providers = Vec::new();
    
    if let Some(provider_array) = config.get("providers").and_then(|v| v.as_array()) {
        for provider_value in provider_array {
            let provider: ModelProvider = provider_value.clone().try_into()?;
            providers.push(provider);
        }
    }
    
    Ok(providers)
}

/// 确定导入操作
fn determine_import_action(
    new_provider: &ModelProvider,
    existing_provider: Option<&ModelProvider>,
    strategy: &ImportStrategy,
) -> ImportAction {
    match existing_provider {
        None => ImportAction::Create,
        Some(_) => match strategy {
            ImportStrategy::SkipExisting => ImportAction::Skip,
            ImportStrategy::UpdateExisting => ImportAction::Update,
            ImportStrategy::ReplaceExisting => ImportAction::Replace,
        },
    }
}
